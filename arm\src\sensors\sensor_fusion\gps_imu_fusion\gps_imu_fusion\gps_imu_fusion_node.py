#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from sensor_msgs.msg import Imu, NavSatFix
from geometry_msgs.msg import PoseWithCovarianceStamped, TwistWithCovarianceStamped
from nav_msgs.msg import Odometry
import numpy as np
import math
try:
    from scipy.spatial.transform import Rotation as R
except ImportError:
    # Fallback implementation if scipy is not available
    class R:
        @staticmethod
        def from_euler(seq, angles):
            # Simple quaternion conversion for yaw only
            yaw = angles[2] if len(angles) > 2 else angles[0]
            return SimpleRotation(yaw)

        def as_quat(self):
            return self.quat

    class SimpleRotation:
        def __init__(self, yaw):
            # Convert yaw to quaternion
            self.quat = [0.0, 0.0, math.sin(yaw/2), math.cos(yaw/2)]

class GPSIMUFusionNode(Node):
    def __init__(self):
        super().__init__('gps_imu_fusion_node')
        
        # Declare parameters
        self.declare_parameter('gps_topic', '/autonomous_car/gps/fix')
        self.declare_parameter('imu_topic', '/autonomous_car/imu/data')
        self.declare_parameter('output_frame', 'base_link')
        self.declare_parameter('publish_rate', 50.0)
        
        # Get parameters
        gps_topic = self.get_parameter('gps_topic').get_parameter_value().string_value
        imu_topic = self.get_parameter('imu_topic').get_parameter_value().string_value
        self.output_frame = self.get_parameter('output_frame').get_parameter_value().string_value
        publish_rate = self.get_parameter('publish_rate').get_parameter_value().double_value
        
        # Subscribers
        self.gps_sub = self.create_subscription(NavSatFix, gps_topic, self.gps_callback, 10)
        self.imu_sub = self.create_subscription(Imu, imu_topic, self.imu_callback, 10)
        
        # Publishers
        self.odom_pub = self.create_publisher(Odometry, '/autonomous_car/odometry/filtered', 10)
        self.pose_pub = self.create_publisher(PoseWithCovarianceStamped, '/autonomous_car/pose/filtered', 10)
        
        # Timer for publishing
        self.timer = self.create_timer(1.0/publish_rate, self.publish_fused_data)
        
        # EKF state: [x, y, z, vx, vy, vz, roll, pitch, yaw]
        self.state = np.zeros(9)
        self.covariance = np.eye(9) * 0.1
        
        # Process noise
        self.Q = np.eye(9) * 0.01
        
        # Measurement noise
        self.R_gps = np.eye(3) * 1.0  # GPS position noise
        self.R_imu = np.eye(6) * 0.1  # IMU noise (accel + gyro)
        
        # Data storage
        self.last_gps_data = None
        self.last_imu_data = None
        self.last_time = None
        
        # Reference GPS position (first GPS reading)
        self.ref_lat = None
        self.ref_lon = None
        self.ref_alt = None
        
        self.get_logger().info('GPS-IMU Fusion node started')
    
    def gps_callback(self, msg):
        """Callback cho GPS data"""
        if msg.status.status >= 0:  # Valid GPS fix
            self.last_gps_data = msg
            
            # Set reference position on first GPS reading
            if self.ref_lat is None:
                self.ref_lat = msg.latitude
                self.ref_lon = msg.longitude
                self.ref_alt = msg.altitude
                self.get_logger().info(f'Reference GPS set: {self.ref_lat}, {self.ref_lon}, {self.ref_alt}')
            
            # Update EKF with GPS measurement
            self.update_with_gps(msg)
    
    def imu_callback(self, msg):
        """Callback cho IMU data"""
        self.last_imu_data = msg
        
        # Predict step with IMU
        self.predict_with_imu(msg)
    
    def gps_to_local(self, lat, lon, alt):
        """Convert GPS coordinates to local ENU coordinates"""
        if self.ref_lat is None:
            return 0.0, 0.0, 0.0
        
        # Simple conversion (for small distances)
        # In practice, use proper UTM conversion
        R_earth = 6371000.0  # Earth radius in meters
        
        dlat = math.radians(lat - self.ref_lat)
        dlon = math.radians(lon - self.ref_lon)
        
        x = R_earth * dlon * math.cos(math.radians(self.ref_lat))
        y = R_earth * dlat
        z = alt - self.ref_alt
        
        return x, y, z
    
    def predict_with_imu(self, imu_msg):
        """Prediction step using IMU data"""
        current_time = self.get_clock().now()
        
        if self.last_time is None:
            self.last_time = current_time
            return
        
        dt = (current_time - self.last_time).nanoseconds / 1e9
        self.last_time = current_time
        
        if dt > 0.1:  # Skip if dt is too large
            return
        
        # Extract IMU data
        ax = imu_msg.linear_acceleration.x
        ay = imu_msg.linear_acceleration.y
        az = imu_msg.linear_acceleration.z - 9.81  # Remove gravity
        
        gx = imu_msg.angular_velocity.x
        gy = imu_msg.angular_velocity.y
        gz = imu_msg.angular_velocity.z
        
        # State prediction
        # Position: x = x + vx*dt + 0.5*ax*dt^2
        self.state[0] += self.state[3] * dt + 0.5 * ax * dt**2
        self.state[1] += self.state[4] * dt + 0.5 * ay * dt**2
        self.state[2] += self.state[5] * dt + 0.5 * az * dt**2
        
        # Velocity: vx = vx + ax*dt
        self.state[3] += ax * dt
        self.state[4] += ay * dt
        self.state[5] += az * dt
        
        # Orientation: integrate angular velocity
        self.state[6] += gx * dt  # roll
        self.state[7] += gy * dt  # pitch
        self.state[8] += gz * dt  # yaw
        
        # Normalize angles
        self.state[6] = self.normalize_angle(self.state[6])
        self.state[7] = self.normalize_angle(self.state[7])
        self.state[8] = self.normalize_angle(self.state[8])
        
        # Covariance prediction (simplified)
        self.covariance += self.Q * dt
    
    def update_with_gps(self, gps_msg):
        """Update step using GPS measurement"""
        # Convert GPS to local coordinates
        x_gps, y_gps, z_gps = self.gps_to_local(
            gps_msg.latitude, gps_msg.longitude, gps_msg.altitude
        )
        
        # Measurement vector
        z = np.array([x_gps, y_gps, z_gps])
        
        # Predicted measurement
        h = np.array([self.state[0], self.state[1], self.state[2]])
        
        # Innovation
        y = z - h
        
        # Measurement Jacobian (simplified - linear measurement model)
        H = np.zeros((3, 9))
        H[0, 0] = 1.0  # dx/dx
        H[1, 1] = 1.0  # dy/dy
        H[2, 2] = 1.0  # dz/dz
        
        # Innovation covariance
        S = H @ self.covariance @ H.T + self.R_gps
        
        # Kalman gain
        K = self.covariance @ H.T @ np.linalg.inv(S)
        
        # State update
        self.state += K @ y
        
        # Covariance update
        I = np.eye(9)
        self.covariance = (I - K @ H) @ self.covariance
    
    def normalize_angle(self, angle):
        """Normalize angle to [-pi, pi]"""
        while angle > math.pi:
            angle -= 2 * math.pi
        while angle < -math.pi:
            angle += 2 * math.pi
        return angle
    
    def publish_fused_data(self):
        """Publish fused odometry and pose"""
        if self.last_imu_data is None:
            return
        
        current_time = self.get_clock().now().to_msg()
        
        # Create Odometry message
        odom_msg = Odometry()
        odom_msg.header.stamp = current_time
        odom_msg.header.frame_id = 'odom'
        odom_msg.child_frame_id = self.output_frame
        
        # Position
        odom_msg.pose.pose.position.x = self.state[0]
        odom_msg.pose.pose.position.y = self.state[1]
        odom_msg.pose.pose.position.z = self.state[2]
        
        # Orientation (convert from Euler to quaternion)
        r = R.from_euler('xyz', [self.state[6], self.state[7], self.state[8]])
        quat = r.as_quat()
        odom_msg.pose.pose.orientation.x = quat[0]
        odom_msg.pose.pose.orientation.y = quat[1]
        odom_msg.pose.pose.orientation.z = quat[2]
        odom_msg.pose.pose.orientation.w = quat[3]
        
        # Velocity
        odom_msg.twist.twist.linear.x = self.state[3]
        odom_msg.twist.twist.linear.y = self.state[4]
        odom_msg.twist.twist.linear.z = self.state[5]
        
        # Angular velocity from IMU
        if self.last_imu_data:
            odom_msg.twist.twist.angular.x = self.last_imu_data.angular_velocity.x
            odom_msg.twist.twist.angular.y = self.last_imu_data.angular_velocity.y
            odom_msg.twist.twist.angular.z = self.last_imu_data.angular_velocity.z
        
        # Covariance (simplified)
        pose_cov = np.zeros(36)
        twist_cov = np.zeros(36)
        
        # Position covariance
        pose_cov[0] = self.covariance[0, 0]  # x
        pose_cov[7] = self.covariance[1, 1]  # y
        pose_cov[14] = self.covariance[2, 2]  # z
        
        # Orientation covariance
        pose_cov[21] = self.covariance[6, 6]  # roll
        pose_cov[28] = self.covariance[7, 7]  # pitch
        pose_cov[35] = self.covariance[8, 8]  # yaw
        
        # Velocity covariance
        twist_cov[0] = self.covariance[3, 3]  # vx
        twist_cov[7] = self.covariance[4, 4]  # vy
        twist_cov[14] = self.covariance[5, 5]  # vz
        
        odom_msg.pose.covariance = pose_cov.tolist()
        odom_msg.twist.covariance = twist_cov.tolist()
        
        # Publish odometry
        self.odom_pub.publish(odom_msg)
        
        # Create and publish pose message
        pose_msg = PoseWithCovarianceStamped()
        pose_msg.header = odom_msg.header
        pose_msg.pose = odom_msg.pose
        self.pose_pub.publish(pose_msg)

def main(args=None):
    rclpy.init(args=args)
    
    try:
        fusion_node = GPSIMUFusionNode()
        rclpy.spin(fusion_node)
    except KeyboardInterrupt:
        pass
    finally:
        if 'fusion_node' in locals():
            fusion_node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
