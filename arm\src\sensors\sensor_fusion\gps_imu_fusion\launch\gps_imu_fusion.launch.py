#!/usr/bin/env python3

from launch import LaunchDescription
from launch_ros.actions import Node
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration

def generate_launch_description():
    # Declare launch arguments
    gps_topic_arg = DeclareLaunchArgument(
        'gps_topic',
        default_value='/autonomous_car/gps/fix',
        description='GPS topic name'
    )
    
    imu_topic_arg = DeclareLaunchArgument(
        'imu_topic',
        default_value='/autonomous_car/imu/data',
        description='IMU topic name'
    )
    
    output_frame_arg = DeclareLaunchArgument(
        'output_frame',
        default_value='base_link',
        description='Output frame ID'
    )
    
    publish_rate_arg = DeclareLaunchArgument(
        'publish_rate',
        default_value='50.0',
        description='Fusion publish rate in Hz'
    )
    
    # GPS-IMU Fusion node
    fusion_node = Node(
        package='gps_imu_fusion',
        executable='gps_imu_fusion_node',
        name='gps_imu_fusion_node',
        output='screen',
        parameters=[{
            'gps_topic': LaunchConfiguration('gps_topic'),
            'imu_topic': LaunchConfiguration('imu_topic'),
            'output_frame': LaunchConfiguration('output_frame'),
            'publish_rate': LaunchConfiguration('publish_rate'),
        }]
    )
    
    return LaunchDescription([
        gps_topic_arg,
        imu_topic_arg,
        output_frame_arg,
        publish_rate_arg,
        fusion_node,
    ])
