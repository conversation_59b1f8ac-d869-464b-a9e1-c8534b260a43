#!/bin/bash

# <PERSON><PERSON><PERSON><PERSON> đến workspace
cd ~/ros_ws/src

# Tạo package cho camera sensor
ros2 pkg create --build-type ament_python camera_sensor --dependencies rclpy sensor_msgs cv_bridge opencv2

# Tạo thư mục cho launch files
mkdir -p camera_sensor/launch

# Tạo file setup.py đúng cách
cat > camera_sensor/setup.py << 'EOF'
from setuptools import find_packages, setup
import os
from glob import glob

package_name = 'camera_sensor'

setup(
    name=package_name,
    version='0.0.0',
    packages=find_packages(exclude=['test']),
    data_files=[
        ('share/ament_index/resource_index/packages',
            ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
        (os.path.join('share', package_name, 'launch'), glob('launch/*.launch.py')),
    ],
    install_requires=['setuptools'],
    zip_safe=True,
    maintainer='your_name',
    maintainer_email='<EMAIL>',
    description='Camera sensor package for autonomous car',
    license='Apache-2.0',
    tests_require=['pytest'],
    entry_points={
        'console_scripts': [
            'camera_node = camera_sensor.camera_node:main',
        ],
    },
)
EOF

echo "Package camera_sensor được tạo thành công!"
echo "Bây giờ bạn có thể thêm code vào camera_sensor/camera_sensor/camera_node.py"
