#!/usr/bin/env python3

from launch import LaunchDescription
from launch_ros.actions import Node
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration

def generate_launch_description():
    # Declare launch arguments
    camera_index_arg = DeclareLaunchArgument(
        'camera_index',
        default_value='0',
        description='Camera device index'
    )
    
    frame_rate_arg = DeclareLaunchArgument(
        'frame_rate',
        default_value='10.0',
        description='Camera frame rate'
    )
    
    image_width_arg = DeclareLaunchArgument(
        'image_width',
        default_value='640',
        description='Image width'
    )
    
    image_height_arg = DeclareLaunchArgument(
        'image_height',
        default_value='480',
        description='Image height'
    )
    
    # Camera node
    camera_node = Node(
        package='camera_sensor',
        executable='camera_node',
        name='camera_node',
        output='screen',
        parameters=[{
            'camera_index': LaunchConfiguration('camera_index'),
            'frame_rate': LaunchConfiguration('frame_rate'),
            'image_width': LaunchConfiguration('image_width'),
            'image_height': LaunchConfiguration('image_height'),
        }],
        remappings=[
            ('/camera/image_raw', '/autonomous_car/camera/image_raw'),
            ('/camera/image_processed', '/autonomous_car/camera/image_processed'),
        ]
    )
    
    return LaunchDescription([
        camera_index_arg,
        frame_rate_arg,
        image_width_arg,
        image_height_arg,
        camera_node,
    ])
