from setuptools import find_packages, setup
import os
from glob import glob

package_name = 'web_interface'

setup(
    name=package_name,
    version='0.0.0',
    packages=find_packages(exclude=['test']),
    data_files=[
        ('share/ament_index/resource_index/packages',
            ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
        (os.path.join('share', package_name, 'launch'), glob('launch/*.launch.py')),
        (os.path.join('share', package_name, 'static'), glob('static/*')),
        (os.path.join('share', package_name, 'templates'), glob('templates/*')),
    ],
    install_requires=['setuptools'],
    zip_safe=True,
    maintainer='Indoor Nav Team',
    maintainer_email='<EMAIL>',
    description='Web UI for selecting destinations and monitoring robot',
    license='Apache-2.0',
    tests_require=['pytest'],
    entry_points={
        'console_scripts': [
            'web_server = web_interface.web_server:main',
            'ros_bridge = web_interface.ros_bridge:main',
        ],
    },
)
