#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from sensor_msgs.msg import Imu
from geometry_msgs.msg import Vector3, Quaternion
import math
import time
import threading
import serial
import numpy as np

class IMUNode(Node):
    def __init__(self):
        super().__init__('imu_node')
        
        # Declare parameters
        self.declare_parameter('serial_port', '/dev/ttyUSB1')
        self.declare_parameter('baud_rate', 115200)
        self.declare_parameter('frame_id', 'imu_frame')
        self.declare_parameter('use_simulator', True)  # True khi không có IMU thật
        self.declare_parameter('publish_rate', 50.0)  # Hz
        
        # Get parameters
        self.serial_port = self.get_parameter('serial_port').get_parameter_value().string_value
        self.baud_rate = self.get_parameter('baud_rate').get_parameter_value().integer_value
        self.frame_id = self.get_parameter('frame_id').get_parameter_value().string_value
        self.use_simulator = self.get_parameter('use_simulator').get_parameter_value().bool_value
        self.publish_rate = self.get_parameter('publish_rate').get_parameter_value().double_value
        
        # Publisher
        self.imu_pub = self.create_publisher(Imu, '/imu/data', 10)
        
        # IMU connection
        self.serial_conn = None
        self.imu_lock = threading.Lock()
        
        # Initialize IMU
        if self.use_simulator:
            self.get_logger().info('Using IMU simulator')
            self.timer = self.create_timer(1.0/self.publish_rate, self.simulate_imu_data)
            self.sim_time = 0.0
            self.sim_angular_vel = [0.0, 0.0, 0.0]  # rad/s
            self.sim_linear_acc = [0.0, 0.0, 9.81]  # m/s^2 (gravity)
        else:
            self.init_imu_connection()
            
        self.get_logger().info(f'IMU node started - Port: {self.serial_port}, Rate: {self.publish_rate}Hz')
    
    def init_imu_connection(self):
        """Khởi tạo kết nối IMU serial"""
        try:
            self.serial_conn = serial.Serial(
                port=self.serial_port,
                baudrate=self.baud_rate,
                timeout=1.0
            )
            self.get_logger().info(f'IMU connected to {self.serial_port}')
            
            # Start IMU reading thread
            self.imu_thread = threading.Thread(target=self.read_imu_data)
            self.imu_thread.daemon = True
            self.imu_thread.start()
            
        except Exception as e:
            self.get_logger().error(f'Failed to connect IMU: {str(e)}')
            self.get_logger().info('Switching to simulator mode')
            self.use_simulator = True
            self.timer = self.create_timer(1.0/self.publish_rate, self.simulate_imu_data)
    
    def read_imu_data(self):
        """Đọc dữ liệu từ IMU thật (placeholder)"""
        while rclpy.ok():
            try:
                if self.serial_conn and self.serial_conn.is_open:
                    # Đọc dữ liệu từ serial port
                    # Format: "ax,ay,az,gx,gy,gz\n"
                    line = self.serial_conn.readline().decode('utf-8').strip()
                    if line:
                        self.parse_imu_data(line)
                        
            except Exception as e:
                self.get_logger().error(f'Error reading IMU data: {str(e)}')
                time.sleep(0.1)
    
    def parse_imu_data(self, data_line):
        """Parse dữ liệu IMU từ string"""
        try:
            parts = data_line.split(',')
            if len(parts) >= 6:
                ax, ay, az = float(parts[0]), float(parts[1]), float(parts[2])
                gx, gy, gz = float(parts[3]), float(parts[4]), float(parts[5])
                
                self.publish_imu_data(ax, ay, az, gx, gy, gz)
                
        except Exception as e:
            self.get_logger().error(f'Error parsing IMU data: {str(e)}')
    
    def simulate_imu_data(self):
        """Simulate IMU data cho testing"""
        self.sim_time += 1.0/self.publish_rate
        
        # Simulate some movement patterns
        # Angular velocity (rad/s) - simulate turning
        self.sim_angular_vel[2] = 0.1 * math.sin(self.sim_time * 0.5)  # Yaw
        
        # Linear acceleration (m/s^2) - simulate forward/backward motion
        forward_acc = 0.5 * math.sin(self.sim_time * 0.3)
        self.sim_linear_acc[0] = forward_acc  # Forward/backward
        self.sim_linear_acc[1] = 0.1 * math.cos(self.sim_time * 0.7)  # Left/right
        self.sim_linear_acc[2] = 9.81 + 0.2 * math.sin(self.sim_time * 2.0)  # Vertical + noise
        
        self.publish_imu_data(
            self.sim_linear_acc[0], self.sim_linear_acc[1], self.sim_linear_acc[2],
            self.sim_angular_vel[0], self.sim_angular_vel[1], self.sim_angular_vel[2]
        )
    
    def publish_imu_data(self, ax, ay, az, gx, gy, gz):
        """Publish IMU data"""
        try:
            imu_msg = Imu()
            
            # Header
            current_time = self.get_clock().now().to_msg()
            imu_msg.header.stamp = current_time
            imu_msg.header.frame_id = self.frame_id
            
            # Linear acceleration (m/s^2)
            imu_msg.linear_acceleration.x = ax
            imu_msg.linear_acceleration.y = ay
            imu_msg.linear_acceleration.z = az
            
            # Angular velocity (rad/s)
            imu_msg.angular_velocity.x = gx
            imu_msg.angular_velocity.y = gy
            imu_msg.angular_velocity.z = gz
            
            # Orientation (quaternion) - simplified integration
            # Trong thực tế cần sử dụng filter phức tạp hơn
            imu_msg.orientation.x = 0.0
            imu_msg.orientation.y = 0.0
            imu_msg.orientation.z = 0.0
            imu_msg.orientation.w = 1.0
            
            # Covariance matrices (simplified)
            # Linear acceleration covariance
            imu_msg.linear_acceleration_covariance = [0.01, 0.0, 0.0,
                                                     0.0, 0.01, 0.0,
                                                     0.0, 0.0, 0.01] + [0.0] * 6
            
            # Angular velocity covariance
            imu_msg.angular_velocity_covariance = [0.001, 0.0, 0.0,
                                                  0.0, 0.001, 0.0,
                                                  0.0, 0.0, 0.001] + [0.0] * 6
            
            # Orientation covariance (high uncertainty since we're not computing it properly)
            imu_msg.orientation_covariance = [0.1, 0.0, 0.0,
                                             0.0, 0.1, 0.0,
                                             0.0, 0.0, 0.1] + [0.0] * 6
            
            self.imu_pub.publish(imu_msg)
            
        except Exception as e:
            self.get_logger().error(f'Error publishing IMU data: {str(e)}')
    
    def destroy_node(self):
        """Cleanup khi node bị tắt"""
        if hasattr(self, 'serial_conn') and self.serial_conn:
            self.serial_conn.close()
        super().destroy_node()

def main(args=None):
    rclpy.init(args=args)
    
    try:
        imu_node = IMUNode()
        rclpy.spin(imu_node)
    except KeyboardInterrupt:
        pass
    finally:
        if 'imu_node' in locals():
            imu_node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
