#!/usr/bin/env python3

from launch import LaunchDescription
from launch_ros.actions import Node
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration

def generate_launch_description():
    # Declare launch arguments
    serial_port_arg = DeclareLaunchArgument(
        'serial_port',
        default_value='/dev/ttyUSB1',
        description='IMU serial port'
    )
    
    baud_rate_arg = DeclareLaunchArgument(
        'baud_rate',
        default_value='115200',
        description='IMU baud rate'
    )
    
    use_simulator_arg = DeclareLaunchArgument(
        'use_simulator',
        default_value='true',
        description='Use IMU simulator instead of real hardware'
    )
    
    publish_rate_arg = DeclareLaunchArgument(
        'publish_rate',
        default_value='50.0',
        description='IMU publish rate in Hz'
    )
    
    frame_id_arg = DeclareLaunchArgument(
        'frame_id',
        default_value='imu_frame',
        description='IMU frame ID'
    )
    
    # IMU node
    imu_node = Node(
        package='imu_sensor',
        executable='imu_node',
        name='imu_node',
        output='screen',
        parameters=[{
            'serial_port': LaunchConfiguration('serial_port'),
            'baud_rate': LaunchConfiguration('baud_rate'),
            'use_simulator': LaunchConfiguration('use_simulator'),
            'publish_rate': LaunchConfiguration('publish_rate'),
            'frame_id': LaunchConfiguration('frame_id'),
        }],
        remappings=[
            ('/imu/data', '/autonomous_car/imu/data'),
        ]
    )
    
    return LaunchDescription([
        serial_port_arg,
        baud_rate_arg,
        use_simulator_arg,
        publish_rate_arg,
        frame_id_arg,
        imu_node,
    ])
