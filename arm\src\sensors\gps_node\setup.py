from setuptools import find_packages, setup
import os
from glob import glob

package_name = 'gps_sensor'

setup(
    name=package_name,
    version='0.0.0',
    packages=find_packages(exclude=['test']),
    data_files=[
        ('share/ament_index/resource_index/packages',
            ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
        (os.path.join('share', package_name, 'launch'), glob('launch/*.launch.py')),
        (os.path.join('share', package_name, 'config'), glob('config/*.yaml')),
    ],
    install_requires=['setuptools'],
    zip_safe=True,
    maintainer='autonomous_car',
    maintainer_email='<EMAIL>',
    description='GPS sensor package for autonomous car',
    license='Apache-2.0',
    tests_require=['pytest'],
    entry_points={
        'console_scripts': [
            'gps_node = gps_sensor.gps_node:main',
            'gps_simulator = gps_sensor.gps_simulator:main',
        ],
    },
)
