running egg_info
creating ../../build/camera_sensor/camera_sensor.egg-info
writing ../../build/camera_sensor/camera_sensor.egg-info/PKG-INFO
writing dependency_links to ../../build/camera_sensor/camera_sensor.egg-info/dependency_links.txt
writing entry points to ../../build/camera_sensor/camera_sensor.egg-info/entry_points.txt
writing requirements to ../../build/camera_sensor/camera_sensor.egg-info/requires.txt
writing top-level names to ../../build/camera_sensor/camera_sensor.egg-info/top_level.txt
writing manifest file '../../build/camera_sensor/camera_sensor.egg-info/SOURCES.txt'
reading manifest file '../../build/camera_sensor/camera_sensor.egg-info/SOURCES.txt'
writing manifest file '../../build/camera_sensor/camera_sensor.egg-info/SOURCES.txt'
running build
running build_py
creating /home/<USER>/autonomous_car_ws/build/camera_sensor/build
creating /home/<USER>/autonomous_car_ws/build/camera_sensor/build/lib
creating /home/<USER>/autonomous_car_ws/build/camera_sensor/build/lib/camera_sensor
copying camera_sensor/__init__.py -> /home/<USER>/autonomous_car_ws/build/camera_sensor/build/lib/camera_sensor
copying camera_sensor/camera_node.py -> /home/<USER>/autonomous_car_ws/build/camera_sensor/build/lib/camera_sensor
running install
running install_lib
creating /home/<USER>/autonomous_car_ws/install/camera_sensor/lib/python3.12/site-packages/camera_sensor
copying /home/<USER>/autonomous_car_ws/build/camera_sensor/build/lib/camera_sensor/__init__.py -> /home/<USER>/autonomous_car_ws/install/camera_sensor/lib/python3.12/site-packages/camera_sensor
copying /home/<USER>/autonomous_car_ws/build/camera_sensor/build/lib/camera_sensor/camera_node.py -> /home/<USER>/autonomous_car_ws/install/camera_sensor/lib/python3.12/site-packages/camera_sensor
byte-compiling /home/<USER>/autonomous_car_ws/install/camera_sensor/lib/python3.12/site-packages/camera_sensor/__init__.py to __init__.cpython-312.pyc
byte-compiling /home/<USER>/autonomous_car_ws/install/camera_sensor/lib/python3.12/site-packages/camera_sensor/camera_node.py to camera_node.cpython-312.pyc
running install_data
creating /home/<USER>/autonomous_car_ws/install/camera_sensor/share/ament_index
creating /home/<USER>/autonomous_car_ws/install/camera_sensor/share/ament_index/resource_index
creating /home/<USER>/autonomous_car_ws/install/camera_sensor/share/ament_index/resource_index/packages
copying resource/camera_sensor -> /home/<USER>/autonomous_car_ws/install/camera_sensor/share/ament_index/resource_index/packages
copying package.xml -> /home/<USER>/autonomous_car_ws/install/camera_sensor/share/camera_sensor
creating /home/<USER>/autonomous_car_ws/install/camera_sensor/share/camera_sensor/launch
copying launch/camera.launch.py -> /home/<USER>/autonomous_car_ws/install/camera_sensor/share/camera_sensor/launch
running install_egg_info
Copying ../../build/camera_sensor/camera_sensor.egg-info to /home/<USER>/autonomous_car_ws/install/camera_sensor/lib/python3.12/site-packages/camera_sensor-0.0.0-py3.12.egg-info
running install_scripts
Installing camera_node script to /home/<USER>/autonomous_car_ws/install/camera_sensor/lib/camera_sensor
writing list of installed files to '/home/<USER>/autonomous_car_ws/build/camera_sensor/install.log'
