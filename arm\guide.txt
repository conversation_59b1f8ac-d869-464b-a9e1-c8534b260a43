1. <PERSON>ệ điều hành và môi trường runtime

Linux-based OS: <PERSON>buntu, Raspbian cho ARM
ROS (Robot Operating System): Framework phổ biến nhất
Real-time capabilities: RTOS hoặc RT-Linux
Docker containers: Đ<PERSON> quản lý các module

2. Stack phần mềm chính
Tầng cảm biến (Sensor Layer):
Camera → OpenCV/GStreamer
LiDAR → PCL (Point Cloud Library)  
GPS → GPSD daemon
IMU → Sensor fusion algorithms
Tầng nhận thức (Perception Layer):

Computer Vision: Nhận diện làn đường, biể<PERSON> báo, vật cản
Object Detection: YOLO, SSD, R-CNN
Semantic Segmentation: Phân loại từng pixel trong ảnh
Sensor Fusion: Kết hợp dữ liệu từ nhiều cảm biến

Tầng lập bản đồ và định vị (Mapping & Localization):

SLAM algorithms:

Visual SLAM (ORB-SLAM, RTAB-Map)
LiDAR SLAM (<PERSON>tographer, LOAM)
Visual-Inertial SLAM


Localization: <PERSON>, <PERSON><PERSON>lter, Particle Filter

Thuật toán tìm đường chi tiết
1. Path Planning Algorithms
Global Path Planning:

A Algorithm*: Tối ưu và đảm bảo tìm được đường
Dijkstra: Đơn giản nhưng chậm hơn
RRT (Rapidly-exploring Random Tree): Tốt cho không gian phức tạp
Hybrid A*: Kết hợp kinematic constraints

Local Path Planning:

Dynamic Window Approach (DWA)
Timed Elastic Band (TEB)
Model Predictive Control (MPC)

Behavior Planning

Finite State Machine: Quản lý các hành vi (đi thẳng, rẽ, dừng, tránh vật cản)
Decision Trees: Quyết định hành động dựa trên tình huống
Neural Networks: Học từ dữ liệu lái xe thực tế

Xử lý thời gian thực
1. Multithreading Architecture:

Sensor Thread: Thu thập dữ liệu liên tục
Processing Thread: Xử lý computer vision
Planning Thread: Tính toán đường đi
Control Thread: Điều khiển động cơ

2. Communication:

Inter-process communication: Shared memory, message queues
ROS Topics/Services: Publish/Subscribe pattern
Real-time constraints: Đảm bảo latency < 100ms

Tối ưu hóa cho ARM
1. Performance Optimization:

NEON SIMD: Sử dụng instruction set của ARM
GPU acceleration: Mali GPU cho computer vision
Memory management: Tối ưu cache usage
Fixed-point arithmetic: Thay vì floating-point

2. Power Management:

Dynamic frequency scaling
Sleep modes cho các module không sử dụng
Efficient algorithms: Giảm computational complexity
