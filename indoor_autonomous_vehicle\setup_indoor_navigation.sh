#!/bin/bash

echo "=== Setting up Indoor Autonomous Vehicle Navigation System ==="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[SETUP]${NC} $1"
}

# Check if we're in the right directory
if [ ! -d "src" ]; then
    print_error "Please run this script from the workspace root directory (indoor_autonomous_vehicle/)"
    exit 1
fi

print_header "Installing Python dependencies..."

# Install required Python packages
pip3 install flask flask-socketio numpy scipy

if [ $? -eq 0 ]; then
    print_status "Python dependencies installed successfully!"
else
    print_warning "Some Python dependencies may have failed to install"
fi

print_header "Building ROS packages..."

# Build all packages
colcon build --packages-select lidar_node ultrasonic_array indoor_navigation web_interface

if [ $? -eq 0 ]; then
    print_status "Build successful!"
else
    print_error "Build failed!"
    exit 1
fi

# Source the workspace
print_status "Sourcing workspace..."
source install/setup.bash

# Create necessary directories
print_status "Creating directories..."
mkdir -p ~/.ros/waypoints
mkdir -p ~/.ros/maps
mkdir -p ~/.ros/logs

# Create default waypoints file
if [ ! -f ~/.ros/waypoints/saved_waypoints.json ]; then
    print_status "Creating default waypoints file..."
    cat > ~/.ros/waypoints/saved_waypoints.json << 'EOF'
{
  "waypoints": {
    "home": {
      "name": "home",
      "x": 0.0,
      "y": 0.0,
      "z": 0.0,
      "orientation": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 1.0
      },
      "description": "Home position",
      "created_time": "2024-01-01T00:00:00",
      "visit_count": 0
    }
  },
  "frequent_locations": {},
  "last_updated": "2024-01-01T00:00:00"
}
EOF
fi

print_status "Indoor Navigation System setup complete!"

echo ""
echo "=== System Architecture ==="
echo "📁 Sensor Layer:"
echo "   ├── 🔍 LiDAR Node (RPLiDAR A1/A2) - Mapping & obstacle detection"
echo "   ├── 📷 Camera Node (Optional) - Object recognition"
echo "   ├── 🧭 IMU Node - Simple orientation"
echo "   └── 📡 Ultrasonic Array - Backup proximity sensors (4-6 sensors)"
echo ""
echo "🗺️ Navigation Stack:"
echo "   ├── 🗺️ Mapping - SLAM for map creation"
echo "   ├── 📍 Localization - Position tracking in map"
echo "   ├── 🎯 Indoor Navigation - Main navigation logic"
echo "   └── 🛣️ Path Planning - A* algorithm for indoor spaces"
echo ""
echo "🎮 Control & Interfaces:"
echo "   ├── 🚗 Motor Controller - Differential drive control"
echo "   ├── 🛡️ Safety Monitor - Emergency stop system"
echo "   ├── 🌐 Web Interface - Select destinations via browser"
echo "   ├── 🗣️ Voice Command (Optional) - Voice control"
echo "   └── 📱 Mobile App (Optional) - Smartphone control"

echo ""
echo "=== Usage Instructions ==="
echo ""
echo "🚀 Launch complete system:"
echo "   ros2 launch indoor_navigation_system.launch.py"
echo ""
echo "🌐 Access web interface:"
echo "   Open browser: http://localhost:8080"
echo ""
echo "📍 Waypoint management:"
echo "   # Save current position"
echo "   ros2 topic pub /waypoint/save std_msgs/String 'data: \"kitchen\"'"
echo ""
echo "   # Go to saved waypoint"
echo "   ros2 topic pub /waypoint/goto std_msgs/String 'data: \"kitchen\"'"
echo ""
echo "   # List all waypoints"
echo "   ros2 topic echo /waypoint/list"
echo ""
echo "🎯 Manual navigation:"
echo "   # Set goal position"
echo "   ros2 topic pub /move_base_simple/goal geometry_msgs/PoseStamped '{header: {frame_id: \"map\"}, pose: {position: {x: 2.0, y: 1.0, z: 0.0}, orientation: {w: 1.0}}}'"
echo ""
echo "📊 Monitor system:"
echo "   # Navigation status"
echo "   ros2 topic echo /navigation/status"
echo ""
echo "   # Sensor data"
echo "   ros2 topic echo /scan                    # LiDAR"
echo "   ros2 topic echo /ultrasonic/proximity    # Ultrasonic"
echo ""
echo "🛠️ Launch individual components:"
echo "   # Sensors only"
echo "   ros2 launch indoor_navigation_system.launch.py use_web_interface:=false"
echo ""
echo "   # Without LiDAR"
echo "   ros2 launch indoor_navigation_system.launch.py use_lidar:=false"
echo ""
echo "   # Real hardware (no simulation)"
echo "   ros2 launch indoor_navigation_system.launch.py use_simulator:=false"

echo ""
echo "=== Key Features ==="
echo "✅ Set start and end points via web interface"
echo "✅ Multiple waypoints with stop points along route"
echo "✅ Static and dynamic obstacle avoidance"
echo "✅ Automatic route replanning when blocked"
echo "✅ Save frequently visited locations"
echo "✅ Recovery from connection loss"
echo "✅ Emergency stop system"
echo "✅ Real-time monitoring via web dashboard"
echo "✅ Mission planning with multiple destinations"
echo "✅ Persistent waypoint storage"

print_status "Setup complete! You can now launch the indoor navigation system."
print_warning "Remember to connect your hardware sensors before launching with use_simulator:=false"
