[0.000000] (-) TimerEvent: {}
[0.016343] (camera_sensor) JobQueued: {'identifier': 'camera_sensor', 'dependencies': OrderedDict()}
[0.016415] (camera_sensor) JobStarted: {'identifier': 'camera_sensor'}
[0.100280] (-) TimerEvent: {}
[0.205057] (-) TimerEvent: {}
[0.321401] (-) TimerEvent: {}
[0.424515] (-) TimerEvent: {}
[0.524787] (-) TimerEvent: {}
[0.654664] (-) TimerEvent: {}
[0.768492] (-) TimerEvent: {}
[0.871471] (-) TimerEvent: {}
[0.972158] (-) TimerEvent: {}
[1.089791] (-) TimerEvent: {}
[1.204390] (-) TimerEvent: {}
[1.305434] (-) TimerEvent: {}
[1.409000] (-) TimerEvent: {}
[1.514390] (-) TimerEvent: {}
[1.614691] (-) TimerEvent: {}
[1.726744] (-) TimerEvent: {}
[1.831717] (-) TimerEvent: {}
[1.932233] (-) TimerEvent: {}
[2.032508] (-) TimerEvent: {}
[2.135576] (-) TimerEvent: {}
[2.237761] (-) TimerEvent: {}
[2.345582] (-) TimerEvent: {}
[2.446130] (-) TimerEvent: {}
[2.554151] (-) TimerEvent: {}
[2.656773] (-) TimerEvent: {}
[2.757211] (-) TimerEvent: {}
[2.864224] (-) TimerEvent: {}
[2.965230] (-) TimerEvent: {}
[3.068546] (-) TimerEvent: {}
[3.169020] (-) TimerEvent: {}
[3.272731] (-) TimerEvent: {}
[3.373128] (-) TimerEvent: {}
[3.485375] (-) TimerEvent: {}
[3.588689] (-) TimerEvent: {}
[3.689402] (-) TimerEvent: {}
[3.789760] (-) TimerEvent: {}
[3.890343] (-) TimerEvent: {}
[3.992401] (-) TimerEvent: {}
[4.094346] (-) TimerEvent: {}
[4.214571] (-) TimerEvent: {}
[4.318045] (-) TimerEvent: {}
[4.422434] (-) TimerEvent: {}
[4.533964] (-) TimerEvent: {}
[4.634808] (-) TimerEvent: {}
[4.736408] (-) TimerEvent: {}
[4.837800] (-) TimerEvent: {}
[4.939954] (-) TimerEvent: {}
[5.041033] (-) TimerEvent: {}
[5.157708] (-) TimerEvent: {}
[5.258061] (-) TimerEvent: {}
[5.373277] (-) TimerEvent: {}
[5.493408] (-) TimerEvent: {}
[5.612968] (-) TimerEvent: {}
[5.716699] (-) TimerEvent: {}
[5.824458] (-) TimerEvent: {}
[5.928882] (-) TimerEvent: {}
[6.030179] (-) TimerEvent: {}
[6.130784] (-) TimerEvent: {}
[6.239601] (-) TimerEvent: {}
[6.340026] (-) TimerEvent: {}
[6.440368] (-) TimerEvent: {}
[6.552372] (-) TimerEvent: {}
[6.676147] (-) TimerEvent: {}
[6.779254] (-) TimerEvent: {}
[6.886123] (-) TimerEvent: {}
[6.990043] (-) TimerEvent: {}
[7.090359] (-) TimerEvent: {}
[7.214267] (-) TimerEvent: {}
[7.314522] (-) TimerEvent: {}
[7.415423] (-) TimerEvent: {}
[7.518014] (-) TimerEvent: {}
[7.538962] (camera_sensor) Command: {'cmd': ['/usr/bin/python3', '-W', 'ignore:setup.py install is deprecated', '-W', 'ignore:easy_install command is deprecated', 'setup.py', 'egg_info', '--egg-base', '../../build/camera_sensor', 'build', '--build-base', '/home/<USER>/autonomous_car_ws/build/camera_sensor/build', 'install', '--record', '/home/<USER>/autonomous_car_ws/build/camera_sensor/install.log', '--single-version-externally-managed', 'install_data'], 'cwd': '/home/<USER>/autonomous_car_ws/src/camera_sensor', 'env': {'LESSOPEN': '| /usr/bin/lesspipe %s', 'USER': 'datlq13', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/sdformat_vendor/share/gz', 'XDG_SESSION_TYPE': 'wayland', 'CLUTTER_DISABLE_MIPMAPPED_TEXT': '1', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>', 'DESKTOP_SESSION': 'ubuntu', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'GTK_MODULES': 'gail:atk-bridge', 'SYSTEMD_EXEC_PID': '1833', 'GSM_SKIP_SSH_AGENT_WORKAROUND': 'true', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'COLORTERM': 'truecolor', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'IM_CONFIG_PHASE': '1', 'WAYLAND_DISPLAY': 'wayland-0', 'LOGNAME': 'datlq13', '_': '/usr/bin/colcon', 'MEMORY_PRESSURE_WATCH': '/sys/fs/cgroup/user.slice/user-1000.slice/<EMAIL>/session.slice/<EMAIL>/memory.pressure', 'XDG_SESSION_CLASS': 'user', 'USERNAME': 'datlq13', 'TERM': 'xterm-256color', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', 'PATH': '/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin', 'SESSION_MANAGER': 'local/UbuntuOS:@/tmp/.ICE-unix/1799,unix/UbuntuOS:/tmp/.ICE-unix/1799', 'XDG_MENU_PREFIX': 'gnome-', 'GNOME_TERMINAL_SCREEN': '/org/gnome/Terminal/screen/beb64af5_044f_4a95_818a_b4f4eb58d187', 'GNOME_SETUP_DISPLAY': ':1', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': ':0', 'LANG': 'en_US.UTF-8', 'XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'XMODIFIERS': '@im=ibus', 'XDG_SESSION_DESKTOP': 'ubuntu', 'XAUTHORITY': '/run/user/1000/.mutter-Xwaylandauth.4DWT62', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'GNOME_TERMINAL_SERVICE': ':1.97', 'SSH_AUTH_SOCK': '/run/user/1000/keyring/ssh', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'QT_ACCESSIBILITY': '1', 'GDMSESSION': 'ubuntu', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'QT_IM_MODULE': 'ibus', 'PWD': '/home/<USER>/autonomous_car_ws/build/camera_sensor', 'LC_ALL': 'en_US.UTF-8', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'XDG_DATA_DIRS': '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/autonomous_car_ws/build/camera_sensor/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/autonomous_car_ws/install/camera_sensor/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'COLCON': '1', 'MEMORY_PRESSURE_WRITE': 'c29tZSAyMDAwMDAgMjAwMDAwMAA=', 'VTE_VERSION': '7600', 'CMAKE_PREFIX_PATH': '/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor'}, 'shell': False}
[7.620538] (-) TimerEvent: {}
[7.735338] (-) TimerEvent: {}
[7.838185] (-) TimerEvent: {}
[7.940994] (-) TimerEvent: {}
[8.054872] (-) TimerEvent: {}
[8.172038] (-) TimerEvent: {}
[8.269491] (camera_sensor) StdoutLine: {'line': b'running egg_info\n'}
[8.270167] (camera_sensor) StdoutLine: {'line': b'creating ../../build/camera_sensor/camera_sensor.egg-info\n'}
[8.274072] (-) TimerEvent: {}
[8.355717] (camera_sensor) StdoutLine: {'line': b'writing ../../build/camera_sensor/camera_sensor.egg-info/PKG-INFO\n'}
[8.355872] (camera_sensor) StdoutLine: {'line': b'writing dependency_links to ../../build/camera_sensor/camera_sensor.egg-info/dependency_links.txt\n'}
[8.355956] (camera_sensor) StdoutLine: {'line': b'writing entry points to ../../build/camera_sensor/camera_sensor.egg-info/entry_points.txt\n'}
[8.356020] (camera_sensor) StdoutLine: {'line': b'writing requirements to ../../build/camera_sensor/camera_sensor.egg-info/requires.txt\n'}
[8.356081] (camera_sensor) StdoutLine: {'line': b'writing top-level names to ../../build/camera_sensor/camera_sensor.egg-info/top_level.txt\n'}
[8.356140] (camera_sensor) StdoutLine: {'line': b"writing manifest file '../../build/camera_sensor/camera_sensor.egg-info/SOURCES.txt'\n"}
[8.379523] (-) TimerEvent: {}
[8.431526] (camera_sensor) StdoutLine: {'line': b"reading manifest file '../../build/camera_sensor/camera_sensor.egg-info/SOURCES.txt'\n"}
[8.432615] (camera_sensor) StdoutLine: {'line': b"writing manifest file '../../build/camera_sensor/camera_sensor.egg-info/SOURCES.txt'\n"}
[8.434600] (camera_sensor) StdoutLine: {'line': b'running build\n'}
[8.434828] (camera_sensor) StdoutLine: {'line': b'running build_py\n'}
[8.435155] (camera_sensor) StdoutLine: {'line': b'creating /home/<USER>/autonomous_car_ws/build/camera_sensor/build\n'}
[8.435433] (camera_sensor) StdoutLine: {'line': b'creating /home/<USER>/autonomous_car_ws/build/camera_sensor/build/lib\n'}
[8.435655] (camera_sensor) StdoutLine: {'line': b'creating /home/<USER>/autonomous_car_ws/build/camera_sensor/build/lib/camera_sensor\n'}
[8.435909] (camera_sensor) StdoutLine: {'line': b'copying camera_sensor/__init__.py -> /home/<USER>/autonomous_car_ws/build/camera_sensor/build/lib/camera_sensor\n'}
[8.436238] (camera_sensor) StdoutLine: {'line': b'copying camera_sensor/camera_node.py -> /home/<USER>/autonomous_car_ws/build/camera_sensor/build/lib/camera_sensor\n'}
[8.436742] (camera_sensor) StdoutLine: {'line': b'running install\n'}
[8.444605] (camera_sensor) StdoutLine: {'line': b'running install_lib\n'}
[8.476607] (camera_sensor) StdoutLine: {'line': b'creating /home/<USER>/autonomous_car_ws/install/camera_sensor/lib/python3.12/site-packages/camera_sensor\n'}
[8.477032] (camera_sensor) StdoutLine: {'line': b'copying /home/<USER>/autonomous_car_ws/build/camera_sensor/build/lib/camera_sensor/__init__.py -> /home/<USER>/autonomous_car_ws/install/camera_sensor/lib/python3.12/site-packages/camera_sensor\n'}
[8.477420] (camera_sensor) StdoutLine: {'line': b'copying /home/<USER>/autonomous_car_ws/build/camera_sensor/build/lib/camera_sensor/camera_node.py -> /home/<USER>/autonomous_car_ws/install/camera_sensor/lib/python3.12/site-packages/camera_sensor\n'}
[8.478451] (camera_sensor) StdoutLine: {'line': b'byte-compiling /home/<USER>/autonomous_car_ws/install/camera_sensor/lib/python3.12/site-packages/camera_sensor/__init__.py to __init__.cpython-312.pyc\n'}
[8.481216] (-) TimerEvent: {}
[8.495084] (camera_sensor) StdoutLine: {'line': b'byte-compiling /home/<USER>/autonomous_car_ws/install/camera_sensor/lib/python3.12/site-packages/camera_sensor/camera_node.py to camera_node.cpython-312.pyc\n'}
[8.495242] (camera_sensor) StdoutLine: {'line': b'running install_data\n'}
[8.495318] (camera_sensor) StdoutLine: {'line': b'creating /home/<USER>/autonomous_car_ws/install/camera_sensor/share/ament_index\n'}
[8.495386] (camera_sensor) StdoutLine: {'line': b'creating /home/<USER>/autonomous_car_ws/install/camera_sensor/share/ament_index/resource_index\n'}
[8.495449] (camera_sensor) StdoutLine: {'line': b'creating /home/<USER>/autonomous_car_ws/install/camera_sensor/share/ament_index/resource_index/packages\n'}
[8.495512] (camera_sensor) StdoutLine: {'line': b'copying resource/camera_sensor -> /home/<USER>/autonomous_car_ws/install/camera_sensor/share/ament_index/resource_index/packages\n'}
[8.495574] (camera_sensor) StdoutLine: {'line': b'copying package.xml -> /home/<USER>/autonomous_car_ws/install/camera_sensor/share/camera_sensor\n'}
[8.495686] (camera_sensor) StdoutLine: {'line': b'creating /home/<USER>/autonomous_car_ws/install/camera_sensor/share/camera_sensor/launch\n'}
[8.501658] (camera_sensor) StdoutLine: {'line': b'copying launch/camera.launch.py -> /home/<USER>/autonomous_car_ws/install/camera_sensor/share/camera_sensor/launch\n'}
[8.513925] (camera_sensor) StdoutLine: {'line': b'running install_egg_info\n'}
[8.537788] (camera_sensor) StdoutLine: {'line': b'Copying ../../build/camera_sensor/camera_sensor.egg-info to /home/<USER>/autonomous_car_ws/install/camera_sensor/lib/python3.12/site-packages/camera_sensor-0.0.0-py3.12.egg-info\n'}
[8.539126] (camera_sensor) StdoutLine: {'line': b'running install_scripts\n'}
[8.585669] (-) TimerEvent: {}
[8.689202] (-) TimerEvent: {}
[8.787502] (camera_sensor) StdoutLine: {'line': b'Installing camera_node script to /home/<USER>/autonomous_car_ws/install/camera_sensor/lib/camera_sensor\n'}
[8.788153] (camera_sensor) StdoutLine: {'line': b"writing list of installed files to '/home/<USER>/autonomous_car_ws/build/camera_sensor/install.log'\n"}
[8.790214] (-) TimerEvent: {}
[8.876843] (camera_sensor) CommandEnded: {'returncode': 0}
[8.890335] (-) TimerEvent: {}
[9.009575] (-) TimerEvent: {}
[9.118498] (-) TimerEvent: {}
[9.222186] (-) TimerEvent: {}
[9.322587] (-) TimerEvent: {}
[9.371436] (camera_sensor) JobEnded: {'identifier': 'camera_sensor', 'rc': 0}
[9.385475] (-) EventReactorShutdown: {}
