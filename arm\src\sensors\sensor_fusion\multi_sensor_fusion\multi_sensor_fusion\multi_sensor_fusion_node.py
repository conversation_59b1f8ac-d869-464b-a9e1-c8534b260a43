#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from sensor_msgs.msg import Image, Imu, NavSatFix
from nav_msgs.msg import Odometry
from geometry_msgs.msg import PoseWithCovarianceStamped
from std_msgs.msg import Header, String
import json
import time

class MultiSensorFusionNode(Node):
    def __init__(self):
        super().__init__('multi_sensor_fusion_node')
        
        # Declare parameters
        self.declare_parameter('camera_topic', '/autonomous_car/camera/image_raw')
        self.declare_parameter('gps_topic', '/autonomous_car/gps/fix')
        self.declare_parameter('imu_topic', '/autonomous_car/imu/data')
        self.declare_parameter('odometry_topic', '/autonomous_car/odometry/filtered')
        self.declare_parameter('publish_rate', 10.0)
        
        # Get parameters
        camera_topic = self.get_parameter('camera_topic').get_parameter_value().string_value
        gps_topic = self.get_parameter('gps_topic').get_parameter_value().string_value
        imu_topic = self.get_parameter('imu_topic').get_parameter_value().string_value
        odometry_topic = self.get_parameter('odometry_topic').get_parameter_value().string_value
        publish_rate = self.get_parameter('publish_rate').get_parameter_value().double_value
        
        # Subscribers
        self.camera_sub = self.create_subscription(Image, camera_topic, self.camera_callback, 10)
        self.gps_sub = self.create_subscription(NavSatFix, gps_topic, self.gps_callback, 10)
        self.imu_sub = self.create_subscription(Imu, imu_topic, self.imu_callback, 10)
        self.odom_sub = self.create_subscription(Odometry, odometry_topic, self.odometry_callback, 10)
        
        # Publishers
        self.sensor_status_pub = self.create_publisher(String, '/autonomous_car/sensor_status', 10)
        self.fused_pose_pub = self.create_publisher(PoseWithCovarianceStamped, '/autonomous_car/pose/fused', 10)
        
        # Timer for publishing status
        self.timer = self.create_timer(1.0/publish_rate, self.publish_sensor_status)
        
        # Sensor data storage
        self.sensor_data = {
            'camera': {'last_msg': None, 'last_time': None, 'status': 'unknown'},
            'gps': {'last_msg': None, 'last_time': None, 'status': 'unknown'},
            'imu': {'last_msg': None, 'last_time': None, 'status': 'unknown'},
            'odometry': {'last_msg': None, 'last_time': None, 'status': 'unknown'}
        }
        
        # Timeout thresholds (seconds)
        self.timeout_thresholds = {
            'camera': 1.0,
            'gps': 2.0,
            'imu': 0.5,
            'odometry': 1.0
        }
        
        self.get_logger().info('Multi-sensor fusion node started')
    
    def camera_callback(self, msg):
        """Callback cho camera data"""
        current_time = time.time()
        self.sensor_data['camera']['last_msg'] = msg
        self.sensor_data['camera']['last_time'] = current_time
        self.sensor_data['camera']['status'] = 'active'
        
        # Process camera data for fusion
        self.process_camera_data(msg)
    
    def gps_callback(self, msg):
        """Callback cho GPS data"""
        current_time = time.time()
        self.sensor_data['gps']['last_msg'] = msg
        self.sensor_data['gps']['last_time'] = current_time
        
        # Check GPS fix quality
        if msg.status.status >= 0:
            self.sensor_data['gps']['status'] = 'active'
        else:
            self.sensor_data['gps']['status'] = 'no_fix'
        
        # Process GPS data for fusion
        self.process_gps_data(msg)
    
    def imu_callback(self, msg):
        """Callback cho IMU data"""
        current_time = time.time()
        self.sensor_data['imu']['last_msg'] = msg
        self.sensor_data['imu']['last_time'] = current_time
        self.sensor_data['imu']['status'] = 'active'
        
        # Process IMU data for fusion
        self.process_imu_data(msg)
    
    def odometry_callback(self, msg):
        """Callback cho fused odometry data"""
        current_time = time.time()
        self.sensor_data['odometry']['last_msg'] = msg
        self.sensor_data['odometry']['last_time'] = current_time
        self.sensor_data['odometry']['status'] = 'active'
        
        # Use odometry as base for final fused pose
        self.publish_fused_pose(msg)
    
    def process_camera_data(self, msg):
        """Process camera data for visual odometry/SLAM"""
        # Placeholder for visual processing
        # In practice, this would include:
        # - Feature extraction and tracking
        # - Visual odometry calculation
        # - Loop closure detection
        pass
    
    def process_gps_data(self, msg):
        """Process GPS data for global localization"""
        # Placeholder for GPS processing
        # In practice, this would include:
        # - Coordinate transformation
        # - GPS quality assessment
        # - Integration with other sensors
        pass
    
    def process_imu_data(self, msg):
        """Process IMU data for motion estimation"""
        # Placeholder for IMU processing
        # In practice, this would include:
        # - Bias estimation and correction
        # - Dead reckoning
        # - Motion model updates
        pass
    
    def publish_fused_pose(self, odom_msg):
        """Publish final fused pose"""
        # Create fused pose message based on odometry
        fused_pose = PoseWithCovarianceStamped()
        fused_pose.header.stamp = self.get_clock().now().to_msg()
        fused_pose.header.frame_id = 'map'
        
        # Copy pose from odometry
        fused_pose.pose = odom_msg.pose
        
        # Adjust covariance based on sensor health
        self.adjust_covariance_based_on_sensor_health(fused_pose)
        
        self.fused_pose_pub.publish(fused_pose)
    
    def adjust_covariance_based_on_sensor_health(self, pose_msg):
        """Adjust pose covariance based on sensor availability"""
        # Increase uncertainty if sensors are not available
        covariance_multiplier = 1.0
        
        if self.sensor_data['gps']['status'] != 'active':
            covariance_multiplier *= 2.0  # Increase uncertainty without GPS
        
        if self.sensor_data['imu']['status'] != 'active':
            covariance_multiplier *= 1.5  # Increase uncertainty without IMU
        
        if self.sensor_data['camera']['status'] != 'active':
            covariance_multiplier *= 1.2  # Slight increase without camera
        
        # Apply multiplier to covariance
        for i in range(len(pose_msg.pose.covariance)):
            pose_msg.pose.covariance[i] *= covariance_multiplier
    
    def check_sensor_timeouts(self):
        """Check for sensor timeouts and update status"""
        current_time = time.time()
        
        for sensor_name, sensor_info in self.sensor_data.items():
            if sensor_info['last_time'] is not None:
                time_since_last = current_time - sensor_info['last_time']
                
                if time_since_last > self.timeout_thresholds[sensor_name]:
                    sensor_info['status'] = 'timeout'
                    self.get_logger().warn(f'{sensor_name} sensor timeout: {time_since_last:.2f}s')
    
    def publish_sensor_status(self):
        """Publish sensor health status"""
        self.check_sensor_timeouts()
        
        # Create status message
        status_data = {
            'timestamp': time.time(),
            'sensors': {}
        }
        
        for sensor_name, sensor_info in self.sensor_data.items():
            status_data['sensors'][sensor_name] = {
                'status': sensor_info['status'],
                'last_update': sensor_info['last_time']
            }
        
        # Add overall system health
        active_sensors = sum(1 for info in self.sensor_data.values() if info['status'] == 'active')
        total_sensors = len(self.sensor_data)
        
        status_data['overall_health'] = {
            'active_sensors': active_sensors,
            'total_sensors': total_sensors,
            'health_percentage': (active_sensors / total_sensors) * 100
        }
        
        # Publish status
        status_msg = String()
        status_msg.data = json.dumps(status_data, indent=2)
        self.sensor_status_pub.publish(status_msg)
        
        # Log critical issues
        if active_sensors < total_sensors / 2:
            self.get_logger().error(f'Critical: Only {active_sensors}/{total_sensors} sensors active!')

def main(args=None):
    rclpy.init(args=args)
    
    try:
        fusion_node = MultiSensorFusionNode()
        rclpy.spin(fusion_node)
    except KeyboardInterrupt:
        pass
    finally:
        if 'fusion_node' in locals():
            fusion_node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
