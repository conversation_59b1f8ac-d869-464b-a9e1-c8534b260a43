#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from sensor_msgs.msg import LaserScan
from geometry_msgs.msg import Twist
import math
import time
import serial
import threading
import numpy as np

class LiDARNode(Node):
    def __init__(self):
        super().__init__('lidar_node')
        
        # Declare parameters
        self.declare_parameter('serial_port', '/dev/ttyUSB0')
        self.declare_parameter('baud_rate', 115200)
        self.declare_parameter('frame_id', 'laser_frame')
        self.declare_parameter('use_simulator', True)
        self.declare_parameter('scan_frequency', 10.0)
        self.declare_parameter('angle_min', -math.pi)
        self.declare_parameter('angle_max', math.pi)
        self.declare_parameter('range_min', 0.15)
        self.declare_parameter('range_max', 12.0)
        
        # Get parameters
        self.serial_port = self.get_parameter('serial_port').get_parameter_value().string_value
        self.baud_rate = self.get_parameter('baud_rate').get_parameter_value().integer_value
        self.frame_id = self.get_parameter('frame_id').get_parameter_value().string_value
        self.use_simulator = self.get_parameter('use_simulator').get_parameter_value().bool_value
        self.scan_frequency = self.get_parameter('scan_frequency').get_parameter_value().double_value
        self.angle_min = self.get_parameter('angle_min').get_parameter_value().double_value
        self.angle_max = self.get_parameter('angle_max').get_parameter_value().double_value
        self.range_min = self.get_parameter('range_min').get_parameter_value().double_value
        self.range_max = self.get_parameter('range_max').get_parameter_value().double_value
        
        # Publishers
        self.scan_pub = self.create_publisher(LaserScan, '/scan', 10)
        
        # LiDAR connection
        self.serial_conn = None
        self.lidar_lock = threading.Lock()
        
        # Simulation data
        self.sim_time = 0.0
        self.room_obstacles = self.create_room_layout()
        
        # Initialize LiDAR
        if self.use_simulator:
            self.get_logger().info('Using LiDAR simulator for indoor environment')
            self.timer = self.create_timer(1.0/self.scan_frequency, self.simulate_lidar_scan)
        else:
            self.init_lidar_connection()
            
        self.get_logger().info(f'LiDAR node started - Port: {self.serial_port}, Freq: {self.scan_frequency}Hz')
    
    def create_room_layout(self):
        """Create a simulated indoor room layout"""
        obstacles = []
        
        # Room walls (5m x 5m room)
        # North wall
        obstacles.extend([(x, 2.5) for x in np.linspace(-2.5, 2.5, 50)])
        # South wall  
        obstacles.extend([(x, -2.5) for x in np.linspace(-2.5, 2.5, 50)])
        # East wall
        obstacles.extend([(2.5, y) for y in np.linspace(-2.5, 2.5, 50)])
        # West wall
        obstacles.extend([(-2.5, y) for y in np.linspace(-2.5, 2.5, 50)])
        
        # Furniture obstacles
        # Table (1m x 0.5m at position (1, 0))
        for x in np.linspace(0.5, 1.5, 10):
            for y in np.linspace(-0.25, 0.25, 5):
                obstacles.append((x, y))
        
        # Chair (0.5m x 0.5m at position (-1, 1))
        for x in np.linspace(-1.25, -0.75, 5):
            for y in np.linspace(0.75, 1.25, 5):
                obstacles.append((x, y))
        
        # Bookshelf (0.3m x 2m at position (-2, 0))
        for x in np.linspace(-2.15, -1.85, 3):
            for y in np.linspace(-1, 1, 20):
                obstacles.append((x, y))
        
        return obstacles
    
    def init_lidar_connection(self):
        """Initialize LiDAR serial connection"""
        try:
            self.serial_conn = serial.Serial(
                port=self.serial_port,
                baudrate=self.baud_rate,
                timeout=1.0
            )
            self.get_logger().info(f'LiDAR connected to {self.serial_port}')
            
            # Start LiDAR reading thread
            self.lidar_thread = threading.Thread(target=self.read_lidar_data)
            self.lidar_thread.daemon = True
            self.lidar_thread.start()
            
        except Exception as e:
            self.get_logger().error(f'Failed to connect LiDAR: {str(e)}')
            self.get_logger().info('Switching to simulator mode')
            self.use_simulator = True
            self.timer = self.create_timer(1.0/self.scan_frequency, self.simulate_lidar_scan)
    
    def read_lidar_data(self):
        """Read data from real LiDAR (RPLiDAR protocol)"""
        while rclpy.ok():
            try:
                if self.serial_conn and self.serial_conn.is_open:
                    # Read LiDAR data packet
                    # This is a simplified version - real RPLiDAR protocol is more complex
                    data = self.serial_conn.read(5)  # Read packet header
                    if len(data) == 5:
                        self.process_lidar_packet(data)
                        
            except Exception as e:
                self.get_logger().error(f'Error reading LiDAR data: {str(e)}')
                time.sleep(0.1)
    
    def process_lidar_packet(self, packet):
        """Process LiDAR data packet"""
        # Placeholder for real LiDAR data processing
        # In practice, this would decode RPLiDAR protocol
        pass
    
    def simulate_lidar_scan(self):
        """Simulate LiDAR scan for indoor environment"""
        self.sim_time += 1.0/self.scan_frequency
        
        # Create laser scan message
        scan = LaserScan()
        scan.header.stamp = self.get_clock().now().to_msg()
        scan.header.frame_id = self.frame_id
        
        scan.angle_min = self.angle_min
        scan.angle_max = self.angle_max
        scan.angle_increment = (self.angle_max - self.angle_min) / 360
        scan.time_increment = 0.0
        scan.scan_time = 1.0 / self.scan_frequency
        scan.range_min = self.range_min
        scan.range_max = self.range_max
        
        # Generate ranges
        ranges = []
        angles = np.linspace(self.angle_min, self.angle_max, 360)
        
        # Robot position (assume at origin for simulation)
        robot_x, robot_y = 0.0, 0.0
        
        for angle in angles:
            min_distance = self.range_max
            
            # Cast ray and find closest obstacle
            for distance in np.linspace(self.range_min, self.range_max, 100):
                ray_x = robot_x + distance * math.cos(angle)
                ray_y = robot_y + distance * math.sin(angle)
                
                # Check collision with obstacles
                for obs_x, obs_y in self.room_obstacles:
                    if math.sqrt((ray_x - obs_x)**2 + (ray_y - obs_y)**2) < 0.1:
                        min_distance = min(min_distance, distance)
                        break
            
            # Add some noise for realism
            noise = np.random.normal(0, 0.02)
            min_distance += noise
            
            # Clamp to valid range
            if min_distance < self.range_min:
                min_distance = float('inf')
            elif min_distance > self.range_max:
                min_distance = float('inf')
                
            ranges.append(min_distance)
        
        scan.ranges = ranges
        scan.intensities = [1.0] * len(ranges)  # Constant intensity
        
        self.scan_pub.publish(scan)
        
        # Log occasionally
        if int(self.sim_time * self.scan_frequency) % 50 == 0:
            valid_ranges = [r for r in ranges if not math.isinf(r)]
            if valid_ranges:
                min_range = min(valid_ranges)
                self.get_logger().info(f'LiDAR scan: {len(valid_ranges)} valid points, min range: {min_range:.2f}m')
    
    def destroy_node(self):
        """Cleanup when node is destroyed"""
        if hasattr(self, 'serial_conn') and self.serial_conn:
            self.serial_conn.close()
        super().destroy_node()

def main(args=None):
    rclpy.init(args=args)
    
    try:
        lidar_node = LiDARNode()
        rclpy.spin(lidar_node)
    except KeyboardInterrupt:
        pass
    finally:
        if 'lidar_node' in locals():
            lidar_node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
