[7.523s] Invoking command in '/home/<USER>/autonomous_car_ws/src/camera_sensor': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/autonomous_car_ws/build/camera_sensor/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/autonomous_car_ws/install/camera_sensor/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/camera_sensor build --build-base /home/<USER>/autonomous_car_ws/build/camera_sensor/build install --record /home/<USER>/autonomous_car_ws/build/camera_sensor/install.log --single-version-externally-managed install_data
[8.253s] running egg_info
[8.254s] creating ../../build/camera_sensor/camera_sensor.egg-info
[8.339s] writing ../../build/camera_sensor/camera_sensor.egg-info/PKG-INFO
[8.340s] writing dependency_links to ../../build/camera_sensor/camera_sensor.egg-info/dependency_links.txt
[8.340s] writing entry points to ../../build/camera_sensor/camera_sensor.egg-info/entry_points.txt
[8.340s] writing requirements to ../../build/camera_sensor/camera_sensor.egg-info/requires.txt
[8.340s] writing top-level names to ../../build/camera_sensor/camera_sensor.egg-info/top_level.txt
[8.340s] writing manifest file '../../build/camera_sensor/camera_sensor.egg-info/SOURCES.txt'
[8.415s] reading manifest file '../../build/camera_sensor/camera_sensor.egg-info/SOURCES.txt'
[8.416s] writing manifest file '../../build/camera_sensor/camera_sensor.egg-info/SOURCES.txt'
[8.418s] running build
[8.418s] running build_py
[8.419s] creating /home/<USER>/autonomous_car_ws/build/camera_sensor/build
[8.419s] creating /home/<USER>/autonomous_car_ws/build/camera_sensor/build/lib
[8.419s] creating /home/<USER>/autonomous_car_ws/build/camera_sensor/build/lib/camera_sensor
[8.420s] copying camera_sensor/__init__.py -> /home/<USER>/autonomous_car_ws/build/camera_sensor/build/lib/camera_sensor
[8.420s] copying camera_sensor/camera_node.py -> /home/<USER>/autonomous_car_ws/build/camera_sensor/build/lib/camera_sensor
[8.420s] running install
[8.428s] running install_lib
[8.460s] creating /home/<USER>/autonomous_car_ws/install/camera_sensor/lib/python3.12/site-packages/camera_sensor
[8.461s] copying /home/<USER>/autonomous_car_ws/build/camera_sensor/build/lib/camera_sensor/__init__.py -> /home/<USER>/autonomous_car_ws/install/camera_sensor/lib/python3.12/site-packages/camera_sensor
[8.461s] copying /home/<USER>/autonomous_car_ws/build/camera_sensor/build/lib/camera_sensor/camera_node.py -> /home/<USER>/autonomous_car_ws/install/camera_sensor/lib/python3.12/site-packages/camera_sensor
[8.462s] byte-compiling /home/<USER>/autonomous_car_ws/install/camera_sensor/lib/python3.12/site-packages/camera_sensor/__init__.py to __init__.cpython-312.pyc
[8.479s] byte-compiling /home/<USER>/autonomous_car_ws/install/camera_sensor/lib/python3.12/site-packages/camera_sensor/camera_node.py to camera_node.cpython-312.pyc
[8.479s] running install_data
[8.479s] creating /home/<USER>/autonomous_car_ws/install/camera_sensor/share/ament_index
[8.479s] creating /home/<USER>/autonomous_car_ws/install/camera_sensor/share/ament_index/resource_index
[8.479s] creating /home/<USER>/autonomous_car_ws/install/camera_sensor/share/ament_index/resource_index/packages
[8.479s] copying resource/camera_sensor -> /home/<USER>/autonomous_car_ws/install/camera_sensor/share/ament_index/resource_index/packages
[8.479s] copying package.xml -> /home/<USER>/autonomous_car_ws/install/camera_sensor/share/camera_sensor
[8.479s] creating /home/<USER>/autonomous_car_ws/install/camera_sensor/share/camera_sensor/launch
[8.485s] copying launch/camera.launch.py -> /home/<USER>/autonomous_car_ws/install/camera_sensor/share/camera_sensor/launch
[8.498s] running install_egg_info
[8.521s] Copying ../../build/camera_sensor/camera_sensor.egg-info to /home/<USER>/autonomous_car_ws/install/camera_sensor/lib/python3.12/site-packages/camera_sensor-0.0.0-py3.12.egg-info
[8.523s] running install_scripts
[8.771s] Installing camera_node script to /home/<USER>/autonomous_car_ws/install/camera_sensor/lib/camera_sensor
[8.772s] writing list of installed files to '/home/<USER>/autonomous_car_ws/build/camera_sensor/install.log'
[8.861s] Invoked command in '/home/<USER>/autonomous_car_ws/src/camera_sensor' returned '0': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/autonomous_car_ws/build/camera_sensor/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/autonomous_car_ws/install/camera_sensor/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/camera_sensor build --build-base /home/<USER>/autonomous_car_ws/build/camera_sensor/build install --record /home/<USER>/autonomous_car_ws/build/camera_sensor/install.log --single-version-externally-managed install_data
