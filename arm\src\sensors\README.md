# Sensor Layer - Autonomous Car

Tầng sensor hoàn chỉnh cho xe tự hành bao gồm camera, GPS, IMU và các hệ thống fusion.

## 🏗️ Kiến trúc

```
src/sensors/
├── camera_node/           # Camera sensor với OpenCV
├── gps_node/             # GPS sensor với simulator
├── imu_node/             # IMU sensor với simulator  
└── sensor_fusion/
    ├── gps_imu_fusion/   # GPS-IMU Extended Kalman Filter
    └── multi_sensor_fusion/ # Tích hợp đa sensor + health monitoring
```

## 📦 Packages

### 1. Camera Sensor (`camera_sensor`)
- **Chức năng**: Xử lý camera cho autonomous car
- **Topics**:
  - `/autonomous_car/camera/image_raw` - Ảnh thô từ camera
  - `/autonomous_car/camera/image_processed` - Ảnh đã xử lý (edge detection)
- **Tính năng**:
  - Capture video với OpenCV
  - Edge detection với Canny algorithm
  - Configurable parameters (index, frame rate, resolution)

### 2. GPS Sensor (`gps_sensor`)
- **Chức năng**: Xử lý dữ liệu GPS
- **Topics**:
  - `/autonomous_car/gps/fix` - GPS NavSatFix data
  - `/autonomous_car/gps/pose` - <PERSON>se với covariance
- **Tính năng**:
  - Hỗ trợ GPS thật và simulator
  - Serial communication với GPS hardware
  - GPS simulator với tọa độ Hà Nội

### 3. IMU Sensor (`imu_sensor`)
- **Chức năng**: Xử lý dữ liệu IMU (gia tốc kế + con quay hồi chuyển)
- **Topics**:
  - `/autonomous_car/imu/data` - IMU data (acceleration + angular velocity)
- **Tính năng**:
  - Hỗ trợ IMU thật và simulator
  - Serial communication với IMU hardware
  - Configurable publish rate (50Hz default)

### 4. GPS-IMU Fusion (`gps_imu_fusion`)
- **Chức năng**: Fusion GPS và IMU với Extended Kalman Filter
- **Topics**:
  - Input: GPS fix, IMU data
  - Output: `/autonomous_car/odometry/filtered`, `/autonomous_car/pose/filtered`
- **Tính năng**:
  - Extended Kalman Filter cho state estimation
  - Position, velocity, orientation estimation
  - Covariance tracking

### 5. Multi-Sensor Fusion (`multi_sensor_fusion`)
- **Chức năng**: Tích hợp tất cả sensor + health monitoring
- **Topics**:
  - Input: Camera, GPS, IMU, Odometry
  - Output: `/autonomous_car/sensor_status`, `/autonomous_car/sensor_alerts`
- **Tính năng**:
  - Sensor health monitoring
  - Alert system
  - Adaptive covariance based on sensor availability

## 🚀 Cài đặt và Sử dụng

### 1. Build packages
```bash
cd arm/
chmod +x sensor_layer_setup.sh
./sensor_layer_setup.sh
```

### 2. Launch toàn bộ sensor layer
```bash
ros2 launch sensor_layer.launch.py
```

### 3. Launch từng sensor riêng lẻ
```bash
# Camera
ros2 launch camera_sensor camera.launch.py

# GPS
ros2 launch gps_sensor gps.launch.py

# IMU
ros2 launch imu_sensor imu.launch.py

# GPS-IMU Fusion
ros2 launch gps_imu_fusion gps_imu_fusion.launch.py

# Multi-sensor fusion
ros2 launch multi_sensor_fusion multi_sensor_fusion.launch.py
```

### 4. Launch với options
```bash
# Disable camera và IMU
ros2 launch sensor_layer.launch.py use_camera:=false use_imu:=false

# Chỉ sử dụng GPS và fusion
ros2 launch sensor_layer.launch.py use_camera:=false use_imu:=false use_multi_sensor_fusion:=true
```

## 📊 Monitoring

### 1. Kiểm tra topics
```bash
ros2 topic list | grep autonomous_car
```

### 2. Monitor sensor health
```bash
ros2 topic echo /autonomous_car/sensor_status
```

### 3. Monitor alerts
```bash
ros2 topic echo /autonomous_car/sensor_alerts
```

### 4. View sensor data
```bash
# Camera
ros2 topic echo /autonomous_car/camera/image_raw

# GPS
ros2 topic echo /autonomous_car/gps/fix

# IMU
ros2 topic echo /autonomous_car/imu/data

# Fused odometry
ros2 topic echo /autonomous_car/odometry/filtered
```

## ⚙️ Configuration

### Camera Parameters
- `camera_index`: Camera device index (default: 0)
- `frame_rate`: Frame rate in Hz (default: 10.0)
- `image_width`: Image width (default: 640)
- `image_height`: Image height (default: 480)

### GPS Parameters
- `serial_port`: GPS serial port (default: /dev/ttyUSB0)
- `baud_rate`: Baud rate (default: 9600)
- `use_simulator`: Use simulator mode (default: true)

### IMU Parameters
- `serial_port`: IMU serial port (default: /dev/ttyUSB1)
- `baud_rate`: Baud rate (default: 115200)
- `use_simulator`: Use simulator mode (default: true)
- `publish_rate`: Publish rate in Hz (default: 50.0)

## 🔧 Hardware Integration

### GPS Hardware
- Connect GPS module to `/dev/ttyUSB0`
- Set `use_simulator:=false` in launch file
- Ensure proper baud rate configuration

### IMU Hardware
- Connect IMU module to `/dev/ttyUSB1`
- Set `use_simulator:=false` in launch file
- Expected data format: "ax,ay,az,gx,gy,gz\n"

### Camera Hardware
- USB camera hoặc CSI camera
- Adjust `camera_index` parameter
- Ensure proper permissions for camera device

## 🎯 Next Steps

Sensor layer này cung cấp foundation cho:
- **Perception Layer**: Object detection, lane detection
- **Localization Layer**: SLAM, mapping
- **Planning Layer**: Path planning, behavior planning

Tích hợp với các tầng khác để tạo thành hệ thống autonomous car hoàn chỉnh.
