from setuptools import find_packages, setup
import os
from glob import glob

package_name = 'indoor_navigation'

setup(
    name=package_name,
    version='0.0.0',
    packages=find_packages(exclude=['test']),
    data_files=[
        ('share/ament_index/resource_index/packages',
            ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
        (os.path.join('share', package_name, 'launch'), glob('launch/*.launch.py')),
        (os.path.join('share', package_name, 'config'), glob('config/*.yaml')),
        (os.path.join('share', package_name, 'maps'), glob('maps/*.yaml')),
        (os.path.join('share', package_name, 'waypoints'), glob('waypoints/*.json')),
    ],
    install_requires=['setuptools'],
    zip_safe=True,
    maintainer='Indoor Nav Team',
    maintainer_email='<EMAIL>',
    description='Main indoor navigation logic with waypoint management',
    license='Apache-2.0',
    tests_require=['pytest'],
    entry_points={
        'console_scripts': [
            'navigation_manager = indoor_navigation.navigation_manager:main',
            'waypoint_manager = indoor_navigation.waypoint_manager:main',
            'mission_executor = indoor_navigation.mission_executor:main',
        ],
    },
)
