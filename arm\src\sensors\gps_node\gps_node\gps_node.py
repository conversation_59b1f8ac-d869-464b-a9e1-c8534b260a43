#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from sensor_msgs.msg import NavSatFix, NavSatStatus
from geometry_msgs.msg import PoseWithCovarianceStamped
from nav_msgs.msg import Odometry
import serial
import time
import math
import threading

class GPSNode(Node):
    def __init__(self):
        super().__init__('gps_node')
        
        # Declare parameters
        self.declare_parameter('serial_port', '/dev/ttyUSB0')
        self.declare_parameter('baud_rate', 9600)
        self.declare_parameter('frame_id', 'gps_frame')
        self.declare_parameter('use_simulator', True)  # True khi không có GPS thật
        
        # Get parameters
        self.serial_port = self.get_parameter('serial_port').get_parameter_value().string_value
        self.baud_rate = self.get_parameter('baud_rate').get_parameter_value().integer_value
        self.frame_id = self.get_parameter('frame_id').get_parameter_value().string_value
        self.use_simulator = self.get_parameter('use_simulator').get_parameter_value().bool_value
        
        # Publishers
        self.gps_pub = self.create_publisher(NavSatFix, '/gps/fix', 10)
        self.pose_pub = self.create_publisher(PoseWithCovarianceStamped, '/gps/pose', 10)
        
        # GPS connection
        self.serial_conn = None
        self.gps_lock = threading.Lock()
        
        # Initialize GPS
        if self.use_simulator:
            self.get_logger().info('Using GPS simulator')
            self.timer = self.create_timer(1.0, self.simulate_gps_data)
            self.sim_lat = 21.0285  # Hanoi latitude
            self.sim_lon = 105.8542  # Hanoi longitude
            self.sim_time = 0.0
        else:
            self.init_gps_connection()
            
        self.get_logger().info(f'GPS node started - Port: {self.serial_port}')
    
    def init_gps_connection(self):
        """Khởi tạo kết nối GPS serial"""
        try:
            self.serial_conn = serial.Serial(
                port=self.serial_port,
                baudrate=self.baud_rate,
                timeout=1.0
            )
            self.get_logger().info(f'GPS connected to {self.serial_port}')
            
            # Start GPS reading thread
            self.gps_thread = threading.Thread(target=self.read_gps_data)
            self.gps_thread.daemon = True
            self.gps_thread.start()
            
        except Exception as e:
            self.get_logger().error(f'Failed to connect GPS: {str(e)}')
            self.get_logger().info('Switching to simulator mode')
            self.use_simulator = True
            self.timer = self.create_timer(1.0, self.simulate_gps_data)
    
    def simulate_gps_data(self):
        """Simulate GPS data for testing"""
        # Tạo chuyển động tròn nhỏ để test
        self.sim_time += 0.01
        
        # Simulate small circular movement
        radius = 0.001  # ~100m radius
        lat_offset = math.sin(self.sim_time) * radius
        lon_offset = math.cos(self.sim_time) * radius
        
        latitude = self.sim_lat + lat_offset
        longitude = self.sim_lon + lon_offset
        altitude = 10.0 + math.sin(self.sim_time * 0.1) * 2.0  # Vary altitude slightly
        
        # Simulate accuracy degradation
        accuracy = 2.0 + abs(math.sin(self.sim_time * 0.05)) * 3.0
        
        self.publish_gps_data(latitude, longitude, altitude, accuracy, True)
    
    def read_gps_data(self):
        """Read real GPS data from serial port"""
        while rclpy.ok() and self.serial_conn:
            try:
                line = self.serial_conn.readline().decode('ascii', errors='replace').strip()
                
                if line.startswith('$GPGGA') or line.startswith('$GNGGA'):
                    self.parse_gga_sentence(line)
                    
            except Exception as e:
                self.get_logger().error(f'Error reading GPS: {str(e)}')
                time.sleep(1.0)
    
    def parse_gga_sentence(self, sentence):
        """Parse NMEA GGA sentence"""
        try:
            parts = sentence.split(',')
            
            if len(parts) < 15:
                return
                
            # Extract data
            time_str = parts[1]
            lat_str = parts[2]
            lat_dir = parts[3]
            lon_str = parts[4]
            lon_dir = parts[5]
            fix_quality = int(parts[6]) if parts[6] else 0
            num_sats = int(parts[7]) if parts[7] else 0
            hdop = float(parts[8]) if parts[8] else 99.0
            altitude = float(parts[9]) if parts[9] else 0.0
            
            if not lat_str or not lon_str:
                return
                
            # Convert to decimal degrees
            latitude = self.nmea_to_decimal(lat_str, lat_dir)
            longitude = self.nmea_to_decimal(lon_str, lon_dir)
            
            # Estimate accuracy from HDOP
            accuracy = hdop * 2.0  # Rough estimation
            
            # Check if we have a valid fix
            has_fix = fix_quality > 0 and num_sats >= 4
            
            self.publish_gps_data(latitude, longitude, altitude, accuracy, has_fix)
            
        except Exception as e:
            self.get_logger().error(f'Error parsing GPS data: {str(e)}')
    
    def nmea_to_decimal(self, coord_str, direction):
        """Convert NMEA coordinate to decimal degrees"""
        if len(coord_str) < 4:
            return 0.0
            
        # Split degrees and minutes
        deg_len = 2 if coord_str.find('.') > 4 else 3
        degrees = float(coord_str[:deg_len])
        minutes = float(coord_str[deg_len:])
        
        decimal = degrees + minutes / 60.0
        
        if direction in ['S', 'W']:
            decimal = -decimal
            
        return decimal
    
    def publish_gps_data(self, latitude, longitude, altitude, accuracy, has_fix):
        """Publish GPS data to ROS topics"""
        current_time = self.get_clock().now().to_msg()
        
        # Create NavSatFix message
        gps_msg = NavSatFix()
        gps_msg.header.stamp = current_time
        gps_msg.header.frame_id = self.frame_id
        
        gps_msg.latitude = latitude
        gps_msg.longitude = longitude
        gps_msg.altitude = altitude
        
        # Set status
        gps_msg.status.status = NavSatStatus.STATUS_FIX if has_fix else NavSatStatus.STATUS_NO_FIX
        gps_msg.status.service = NavSatStatus.SERVICE_GPS
        
        # Set covariance (accuracy)
        covariance = accuracy ** 2
        gps_msg.position_covariance = [
            covariance, 0.0, 0.0,
            0.0, covariance, 0.0,
            0.0, 0.0, covariance * 2  # Higher uncertainty in altitude
        ]
        gps_msg.position_covariance_type = NavSatFix.COVARIANCE_TYPE_DIAGONAL_KNOWN
        
        # Publish GPS fix
        self.gps_pub.publish(gps_msg)
        
        # Create and publish pose
        pose_msg = PoseWithCovarianceStamped()
        pose_msg.header = gps_msg.header
        
        # Convert GPS to local coordinates (simplified)
        # In real application, use proper coordinate transformation
        pose_msg.pose.pose.position.x = (longitude - self.sim_lon) * 111000.0  # Rough conversion
        pose_msg.pose.pose.position.y = (latitude - self.sim_lat) * 111000.0
        pose_msg.pose.pose.position.z = altitude
        
        pose_msg.pose.pose.orientation.w = 1.0  # No rotation info from GPS
        
        # Set covariance
        pose_covariance = [0.0] * 36
        pose_covariance[0] = covariance  # x
        pose_covariance[7] = covariance  # y
        pose_covariance[14] = covariance * 2  # z
        pose_covariance[21] = 1.0  # roll
        pose_covariance[28] = 1.0  # pitch
        pose_covariance[35] = 1.0  # yaw
        pose_msg.pose.covariance = pose_covariance
        
        self.pose_pub.publish(pose_msg)
        
        # Log info periodically
        if hasattr(self, '_last_log_time'):
            if time.time() - self._last_log_time > 5.0:  # Log every 5 seconds
                self.get_logger().info(f'GPS: Lat={latitude:.6f}, Lon={longitude:.6f}, Alt={altitude:.1f}m, Acc={accuracy:.1f}m')
                self._last_log_time = time.time()
        else:
            self._last_log_time = time.time()
    
    def destroy_node(self):
        """Cleanup when node is destroyed"""
        if self.serial_conn:
            self.serial_conn.close()
        super().destroy_node()

def main(args=None):
    rclpy.init(args=args)
    
    try:
        gps_node = GPSNode()
        rclpy.spin(gps_node)
    except KeyboardInterrupt:
        pass
    finally:
        if 'gps_node' in locals():
            gps_node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
