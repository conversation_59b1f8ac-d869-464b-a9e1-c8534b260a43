[0.744s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.744s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=1, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x79fc19371640>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x79fc194ef500>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x79fc194ef500>>, mixin_verb=('build',))
[0.880s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.880s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.881s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.881s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.881s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.881s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.881s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/autonomous_car_ws'
[0.881s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.881s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.881s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.881s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.881s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.881s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.881s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.881s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.881s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.959s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.959s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.959s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.959s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.959s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.960s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.960s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.960s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.960s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.960s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.960s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.960s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.961s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.961s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.961s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.966s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.966s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.966s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.966s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.966s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.966s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.967s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.967s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.967s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.967s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.967s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.967s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.967s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.967s] Level 1:colcon.colcon_core.package_identification:_identify(src/camera_sensor) by extensions ['ignore', 'ignore_ament_install']
[0.967s] Level 1:colcon.colcon_core.package_identification:_identify(src/camera_sensor) by extension 'ignore'
[0.967s] Level 1:colcon.colcon_core.package_identification:_identify(src/camera_sensor) by extension 'ignore_ament_install'
[0.967s] Level 1:colcon.colcon_core.package_identification:_identify(src/camera_sensor) by extensions ['colcon_pkg']
[0.967s] Level 1:colcon.colcon_core.package_identification:_identify(src/camera_sensor) by extension 'colcon_pkg'
[0.968s] Level 1:colcon.colcon_core.package_identification:_identify(src/camera_sensor) by extensions ['colcon_meta']
[0.968s] Level 1:colcon.colcon_core.package_identification:_identify(src/camera_sensor) by extension 'colcon_meta'
[0.968s] Level 1:colcon.colcon_core.package_identification:_identify(src/camera_sensor) by extensions ['ros']
[0.968s] Level 1:colcon.colcon_core.package_identification:_identify(src/camera_sensor) by extension 'ros'
[0.975s] DEBUG:colcon.colcon_core.package_identification:Package 'src/camera_sensor' with type 'ros.ament_python' and name 'camera_sensor'
[0.975s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.975s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.975s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.975s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.976s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[1.163s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[1.163s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[1.218s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 209 installed packages in /opt/ros/jazzy
[1.219s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[2.299s] Level 5:colcon.colcon_core.verb:set package 'camera_sensor' build argument 'cmake_args' from command line to 'None'
[2.299s] Level 5:colcon.colcon_core.verb:set package 'camera_sensor' build argument 'cmake_target' from command line to 'None'
[2.299s] Level 5:colcon.colcon_core.verb:set package 'camera_sensor' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[2.299s] Level 5:colcon.colcon_core.verb:set package 'camera_sensor' build argument 'cmake_clean_cache' from command line to 'False'
[2.300s] Level 5:colcon.colcon_core.verb:set package 'camera_sensor' build argument 'cmake_clean_first' from command line to 'False'
[2.300s] Level 5:colcon.colcon_core.verb:set package 'camera_sensor' build argument 'cmake_force_configure' from command line to 'False'
[2.300s] Level 5:colcon.colcon_core.verb:set package 'camera_sensor' build argument 'ament_cmake_args' from command line to 'None'
[2.300s] Level 5:colcon.colcon_core.verb:set package 'camera_sensor' build argument 'catkin_cmake_args' from command line to 'None'
[2.300s] Level 5:colcon.colcon_core.verb:set package 'camera_sensor' build argument 'catkin_skip_building_tests' from command line to 'False'
[2.300s] DEBUG:colcon.colcon_core.verb:Building package 'camera_sensor' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/autonomous_car_ws/build/camera_sensor', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/autonomous_car_ws/install/camera_sensor', 'merge_install': False, 'path': '/home/<USER>/autonomous_car_ws/src/camera_sensor', 'symlink_install': False, 'test_result_base': None}
[2.300s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[2.314s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[2.315s] INFO:colcon.colcon_ros.task.ament_python.build:Building ROS package in '/home/<USER>/autonomous_car_ws/src/camera_sensor' with build type 'ament_python'
[2.315s] Level 1:colcon.colcon_core.shell:create_environment_hook('camera_sensor', 'ament_prefix_path')
[2.381s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[2.381s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/autonomous_car_ws/install/camera_sensor/share/camera_sensor/hook/ament_prefix_path.ps1'
[2.396s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/autonomous_car_ws/install/camera_sensor/share/camera_sensor/hook/ament_prefix_path.dsv'
[2.414s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/autonomous_car_ws/install/camera_sensor/share/camera_sensor/hook/ament_prefix_path.sh'
[2.417s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[2.417s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[3.217s] INFO:colcon.colcon_core.task.python.build:Building Python package in '/home/<USER>/autonomous_car_ws/src/camera_sensor'
[3.217s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[3.217s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[9.840s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/autonomous_car_ws/src/camera_sensor': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/autonomous_car_ws/build/camera_sensor/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/autonomous_car_ws/install/camera_sensor/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/camera_sensor build --build-base /home/<USER>/autonomous_car_ws/build/camera_sensor/build install --record /home/<USER>/autonomous_car_ws/build/camera_sensor/install.log --single-version-externally-managed install_data
[11.178s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/autonomous_car_ws/src/camera_sensor' returned '0': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/autonomous_car_ws/build/camera_sensor/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/autonomous_car_ws/install/camera_sensor/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/camera_sensor build --build-base /home/<USER>/autonomous_car_ws/build/camera_sensor/build install --record /home/<USER>/autonomous_car_ws/build/camera_sensor/install.log --single-version-externally-managed install_data
[11.618s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/autonomous_car_ws/install/camera_sensor' for CMake module files
[11.618s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/autonomous_car_ws/install/camera_sensor' for CMake config files
[11.619s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/autonomous_car_ws/install/camera_sensor/lib'
[11.619s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/autonomous_car_ws/install/camera_sensor/bin'
[11.619s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/autonomous_car_ws/install/camera_sensor/lib/pkgconfig/camera_sensor.pc'
[11.619s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/autonomous_car_ws/install/camera_sensor/lib/python3.12/site-packages'
[11.620s] Level 1:colcon.colcon_core.shell:create_environment_hook('camera_sensor', 'pythonpath')
[11.620s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/autonomous_car_ws/install/camera_sensor/share/camera_sensor/hook/pythonpath.ps1'
[11.620s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/autonomous_car_ws/install/camera_sensor/share/camera_sensor/hook/pythonpath.dsv'
[11.621s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/autonomous_car_ws/install/camera_sensor/share/camera_sensor/hook/pythonpath.sh'
[11.621s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/autonomous_car_ws/install/camera_sensor/bin'
[11.621s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(camera_sensor)
[11.621s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/autonomous_car_ws/install/camera_sensor/share/camera_sensor/package.ps1'
[11.639s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/autonomous_car_ws/install/camera_sensor/share/camera_sensor/package.dsv'
[11.649s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/autonomous_car_ws/install/camera_sensor/share/camera_sensor/package.sh'
[11.653s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/autonomous_car_ws/install/camera_sensor/share/camera_sensor/package.bash'
[11.657s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/autonomous_car_ws/install/camera_sensor/share/camera_sensor/package.zsh'
[11.671s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/autonomous_car_ws/install/camera_sensor/share/colcon-core/packages/camera_sensor)
[11.671s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[11.685s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[11.685s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[11.685s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[11.758s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[11.758s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[11.758s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[11.842s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[11.842s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/autonomous_car_ws/install/local_setup.ps1'
[11.861s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/autonomous_car_ws/install/_local_setup_util_ps1.py'
[11.864s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/autonomous_car_ws/install/setup.ps1'
[11.866s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/autonomous_car_ws/install/local_setup.sh'
[11.869s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/autonomous_car_ws/install/_local_setup_util_sh.py'
[11.870s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/autonomous_car_ws/install/setup.sh'
[11.885s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/autonomous_car_ws/install/local_setup.bash'
[11.887s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/autonomous_car_ws/install/setup.bash'
[11.901s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/autonomous_car_ws/install/local_setup.zsh'
[11.905s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/autonomous_car_ws/install/setup.zsh'
