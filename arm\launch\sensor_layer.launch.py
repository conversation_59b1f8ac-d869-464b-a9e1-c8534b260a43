#!/usr/bin/env python3

from launch import LaunchDescription
from launch_ros.actions import Node
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch_ros.substitutions import FindPackageShare
from launch import conditions

def generate_launch_description():
    # Declare launch arguments
    use_camera_arg = DeclareLaunchArgument(
        'use_camera',
        default_value='true',
        description='Enable camera sensor'
    )
    
    use_gps_arg = DeclareLaunchArgument(
        'use_gps',
        default_value='true',
        description='Enable GPS sensor'
    )
    
    use_imu_arg = DeclareLaunchArgument(
        'use_imu',
        default_value='true',
        description='Enable IMU sensor'
    )
    
    use_gps_imu_fusion_arg = DeclareLaunchArgument(
        'use_gps_imu_fusion',
        default_value='true',
        description='Enable GPS-IMU fusion'
    )
    
    use_multi_sensor_fusion_arg = DeclareLaunchArgument(
        'use_multi_sensor_fusion',
        default_value='true',
        description='Enable multi-sensor fusion'
    )
    
    use_health_monitor_arg = DeclareLaunchArgument(
        'use_health_monitor',
        default_value='true',
        description='Enable sensor health monitoring'
    )
    
    # Camera node
    camera_node = Node(
        package='camera_sensor',
        executable='camera_node',
        name='camera_node',
        output='screen',
        parameters=[{
            'camera_index': 0,
            'frame_rate': 10.0,
            'image_width': 640,
            'image_height': 480,
        }],
        remappings=[
            ('/camera/image_raw', '/autonomous_car/camera/image_raw'),
            ('/camera/image_processed', '/autonomous_car/camera/image_processed'),
        ],
        condition=conditions.IfCondition(LaunchConfiguration('use_camera'))
    )
    
    # GPS node
    gps_node = Node(
        package='gps_sensor',
        executable='gps_node',
        name='gps_node',
        output='screen',
        parameters=[{
            'serial_port': '/dev/ttyUSB0',
            'baud_rate': 9600,
            'frame_id': 'gps_frame',
            'use_simulator': True,
        }],
        remappings=[
            ('/gps/fix', '/autonomous_car/gps/fix'),
            ('/gps/pose', '/autonomous_car/gps/pose'),
        ],
        condition=conditions.IfCondition(LaunchConfiguration('use_gps'))
    )
    
    # IMU node
    imu_node = Node(
        package='imu_sensor',
        executable='imu_node',
        name='imu_node',
        output='screen',
        parameters=[{
            'serial_port': '/dev/ttyUSB1',
            'baud_rate': 115200,
            'frame_id': 'imu_frame',
            'use_simulator': True,
            'publish_rate': 50.0,
        }],
        remappings=[
            ('/imu/data', '/autonomous_car/imu/data'),
        ],
        condition=conditions.IfCondition(LaunchConfiguration('use_imu'))
    )
    
    # GPS-IMU Fusion node
    gps_imu_fusion_node = Node(
        package='gps_imu_fusion',
        executable='gps_imu_fusion_node',
        name='gps_imu_fusion_node',
        output='screen',
        parameters=[{
            'gps_topic': '/autonomous_car/gps/fix',
            'imu_topic': '/autonomous_car/imu/data',
            'output_frame': 'base_link',
            'publish_rate': 50.0,
        }],
        condition=conditions.IfCondition(LaunchConfiguration('use_gps_imu_fusion'))
    )
    
    # Multi-sensor fusion node
    multi_sensor_fusion_node = Node(
        package='multi_sensor_fusion',
        executable='multi_sensor_fusion_node',
        name='multi_sensor_fusion_node',
        output='screen',
        parameters=[{
            'camera_topic': '/autonomous_car/camera/image_raw',
            'gps_topic': '/autonomous_car/gps/fix',
            'imu_topic': '/autonomous_car/imu/data',
            'odometry_topic': '/autonomous_car/odometry/filtered',
            'publish_rate': 10.0,
        }],
        condition=conditions.IfCondition(LaunchConfiguration('use_multi_sensor_fusion'))
    )
    
    # Sensor health monitor
    health_monitor_node = Node(
        package='multi_sensor_fusion',
        executable='sensor_health_monitor',
        name='sensor_health_monitor',
        output='screen',
        condition=conditions.IfCondition(LaunchConfiguration('use_health_monitor'))
    )
    
    return LaunchDescription([
        use_camera_arg,
        use_gps_arg,
        use_imu_arg,
        use_gps_imu_fusion_arg,
        use_multi_sensor_fusion_arg,
        use_health_monitor_arg,
        camera_node,
        gps_node,
        imu_node,
        gps_imu_fusion_node,
        multi_sensor_fusion_node,
        health_monitor_node,
    ])
