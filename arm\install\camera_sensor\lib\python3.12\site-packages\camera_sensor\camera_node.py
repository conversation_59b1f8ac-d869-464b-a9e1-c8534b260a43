#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from sensor_msgs.msg import Image
from cv_bridge import CvBridge
import cv2
import numpy as np

class CameraNode(Node):
    def __init__(self):
        super().__init__('camera_node')
        
        # Declare parameters
        self.declare_parameter('camera_index', 0)
        self.declare_parameter('frame_rate', 10.0)
        self.declare_parameter('image_width', 640)
        self.declare_parameter('image_height', 480)
        
        # Get parameters
        camera_index = self.get_parameter('camera_index').get_parameter_value().integer_value
        frame_rate = self.get_parameter('frame_rate').get_parameter_value().double_value
        image_width = self.get_parameter('image_width').get_parameter_value().integer_value
        image_height = self.get_parameter('image_height').get_parameter_value().integer_value
        
        # Publisher cho raw image
        self.image_pub = self.create_publisher(Image, '/camera/image_raw', 10)
        
        # Publisher cho processed image
        self.processed_pub = self.create_publisher(Image, '/camera/image_processed', 10)
        
        # Timer để capture frames
        self.timer = self.create_timer(1.0/frame_rate, self.capture_and_publish)
        
        # OpenCV setup
        self.cap = cv2.VideoCapture(camera_index)
        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, image_width)
        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, image_height)
        self.cap.set(cv2.CAP_PROP_FPS, frame_rate)
        
        # CV Bridge để convert between OpenCV và ROS messages
        self.cv_bridge = CvBridge()
        
        # Kiểm tra camera có hoạt động không
        if not self.cap.isOpened():
            self.get_logger().error(f'Cannot open camera {camera_index}')
            return
            
        self.get_logger().info(f'Camera node started - Index: {camera_index}, FPS: {frame_rate}')
        
    def capture_and_publish(self):
        """Capture frame và publish lên ROS topics"""
        ret, frame = self.cap.read()
        
        if not ret:
            self.get_logger().warn('Failed to capture frame')
            return
            
        try:
            # Tạo header cho message
            current_time = self.get_clock().now().to_msg()
            
            # Publish raw image
            raw_msg = self.cv_bridge.cv2_to_imgmsg(frame, encoding='bgr8')
            raw_msg.header.stamp = current_time
            raw_msg.header.frame_id = 'camera_frame'
            self.image_pub.publish(raw_msg)
            
            # Xử lý ảnh đơn giản (edge detection)
            processed_frame = self.process_image(frame)
            
            # Publish processed image
            processed_msg = self.cv_bridge.cv2_to_imgmsg(processed_frame, encoding='mono8')
            processed_msg.header.stamp = current_time
            processed_msg.header.frame_id = 'camera_frame'
            self.processed_pub.publish(processed_msg)
            
        except Exception as e:
            self.get_logger().error(f'Error processing frame: {str(e)}')
    
    def process_image(self, image):
        """Xử lý ảnh đơn giản - edge detection"""
        # Convert sang grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Gaussian blur để giảm noise
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # Canny edge detection
        edges = cv2.Canny(blurred, 50, 150)
        
        return edges
    
    def destroy_node(self):
        """Cleanup khi node bị tắt"""
        if hasattr(self, 'cap'):
            self.cap.release()
        super().destroy_node()

def main(args=None):
    rclpy.init(args=args)
    
    try:
        camera_node = CameraNode()
        rclpy.spin(camera_node)
    except KeyboardInterrupt:
        pass
    finally:
        if 'camera_node' in locals():
            camera_node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
