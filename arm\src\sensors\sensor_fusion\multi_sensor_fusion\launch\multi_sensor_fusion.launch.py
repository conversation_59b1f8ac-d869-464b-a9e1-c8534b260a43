#!/usr/bin/env python3

from launch import LaunchDescription
from launch_ros.actions import Node
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration
from launch import conditions

def generate_launch_description():
    # Declare launch arguments
    use_camera_arg = DeclareLaunchArgument(
        'use_camera',
        default_value='true',
        description='Enable camera sensor'
    )
    
    use_gps_arg = DeclareLaunchArgument(
        'use_gps',
        default_value='true',
        description='Enable GPS sensor'
    )
    
    use_imu_arg = DeclareLaunchArgument(
        'use_imu',
        default_value='true',
        description='Enable IMU sensor'
    )
    
    use_fusion_arg = DeclareLaunchArgument(
        'use_fusion',
        default_value='true',
        description='Enable sensor fusion'
    )
    
    use_health_monitor_arg = DeclareLaunchArgument(
        'use_health_monitor',
        default_value='true',
        description='Enable sensor health monitoring'
    )
    
    # Multi-sensor fusion node
    fusion_node = Node(
        package='multi_sensor_fusion',
        executable='multi_sensor_fusion_node',
        name='multi_sensor_fusion_node',
        output='screen',
        parameters=[{
            'camera_topic': '/autonomous_car/camera/image_raw',
            'gps_topic': '/autonomous_car/gps/fix',
            'imu_topic': '/autonomous_car/imu/data',
            'odometry_topic': '/autonomous_car/odometry/filtered',
            'publish_rate': 10.0,
        }],
        condition=conditions.IfCondition(LaunchConfiguration('use_fusion'))
    )
    
    # Sensor health monitor
    health_monitor = Node(
        package='multi_sensor_fusion',
        executable='sensor_health_monitor',
        name='sensor_health_monitor',
        output='screen',
        condition=conditions.IfCondition(LaunchConfiguration('use_health_monitor'))
    )
    
    return LaunchDescription([
        use_camera_arg,
        use_gps_arg,
        use_imu_arg,
        use_fusion_arg,
        use_health_monitor_arg,
        fusion_node,
        health_monitor,
    ])
