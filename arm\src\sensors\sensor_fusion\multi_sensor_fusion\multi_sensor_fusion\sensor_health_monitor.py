#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from std_msgs.msg import String
import json
import time

class SensorHealthMonitor(Node):
    def __init__(self):
        super().__init__('sensor_health_monitor')
        
        # Subscriber for sensor status
        self.status_sub = self.create_subscription(
            String, 
            '/autonomous_car/sensor_status', 
            self.status_callback, 
            10
        )
        
        # Publisher for alerts
        self.alert_pub = self.create_publisher(String, '/autonomous_car/sensor_alerts', 10)
        
        # Health history
        self.health_history = []
        self.max_history_length = 100
        
        # Alert thresholds
        self.critical_health_threshold = 50.0  # Below 50% health is critical
        self.warning_health_threshold = 75.0   # Below 75% health is warning
        
        # Alert state tracking
        self.last_alert_time = {}
        self.alert_cooldown = 10.0  # seconds
        
        self.get_logger().info('Sensor Health Monitor started')
    
    def status_callback(self, msg):
        """Process sensor status updates"""
        try:
            status_data = json.loads(msg.data)
            self.process_health_data(status_data)
            
        except json.JSONDecodeError as e:
            self.get_logger().error(f'Failed to parse sensor status: {e}')
    
    def process_health_data(self, status_data):
        """Process and analyze health data"""
        current_time = time.time()
        
        # Add to history
        self.health_history.append({
            'timestamp': current_time,
            'data': status_data
        })
        
        # Trim history
        if len(self.health_history) > self.max_history_length:
            self.health_history.pop(0)
        
        # Analyze current health
        overall_health = status_data.get('overall_health', {})
        health_percentage = overall_health.get('health_percentage', 0)
        
        # Check for alerts
        self.check_health_alerts(status_data, health_percentage)
        
        # Log health status
        active_sensors = overall_health.get('active_sensors', 0)
        total_sensors = overall_health.get('total_sensors', 0)
        
        self.get_logger().info(
            f'System Health: {health_percentage:.1f}% '
            f'({active_sensors}/{total_sensors} sensors active)'
        )
        
        # Detailed sensor status
        sensors = status_data.get('sensors', {})
        for sensor_name, sensor_info in sensors.items():
            status = sensor_info.get('status', 'unknown')
            if status != 'active':
                self.get_logger().warn(f'{sensor_name}: {status}')
    
    def check_health_alerts(self, status_data, health_percentage):
        """Check for health alerts and publish if necessary"""
        current_time = time.time()
        
        # Critical health alert
        if health_percentage < self.critical_health_threshold:
            self.send_alert('critical', 
                          f'CRITICAL: System health at {health_percentage:.1f}%', 
                          status_data, current_time)
        
        # Warning health alert
        elif health_percentage < self.warning_health_threshold:
            self.send_alert('warning', 
                          f'WARNING: System health at {health_percentage:.1f}%', 
                          status_data, current_time)
        
        # Individual sensor alerts
        sensors = status_data.get('sensors', {})
        for sensor_name, sensor_info in sensors.items():
            status = sensor_info.get('status', 'unknown')
            
            if status == 'timeout':
                self.send_alert('sensor_timeout', 
                              f'TIMEOUT: {sensor_name} sensor not responding', 
                              sensor_info, current_time)
            
            elif status == 'no_fix' and sensor_name == 'gps':
                self.send_alert('gps_no_fix', 
                              f'GPS: No satellite fix available', 
                              sensor_info, current_time)
    
    def send_alert(self, alert_type, message, data, current_time):
        """Send alert if not in cooldown period"""
        last_alert = self.last_alert_time.get(alert_type, 0)
        
        if current_time - last_alert > self.alert_cooldown:
            # Create alert message
            alert_data = {
                'timestamp': current_time,
                'alert_type': alert_type,
                'message': message,
                'data': data
            }
            
            alert_msg = String()
            alert_msg.data = json.dumps(alert_data, indent=2)
            self.alert_pub.publish(alert_msg)
            
            # Update last alert time
            self.last_alert_time[alert_type] = current_time
            
            # Log alert
            self.get_logger().error(f'ALERT [{alert_type}]: {message}')
    
    def get_health_trend(self, window_size=10):
        """Calculate health trend over recent history"""
        if len(self.health_history) < window_size:
            return 0.0  # Not enough data
        
        recent_health = []
        for entry in self.health_history[-window_size:]:
            health_data = entry['data'].get('overall_health', {})
            health_percentage = health_data.get('health_percentage', 0)
            recent_health.append(health_percentage)
        
        if len(recent_health) < 2:
            return 0.0
        
        # Simple linear trend calculation
        x = list(range(len(recent_health)))
        y = recent_health
        
        n = len(x)
        sum_x = sum(x)
        sum_y = sum(y)
        sum_xy = sum(x[i] * y[i] for i in range(n))
        sum_x2 = sum(x[i] * x[i] for i in range(n))
        
        # Calculate slope (trend)
        if n * sum_x2 - sum_x * sum_x != 0:
            slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
            return slope
        
        return 0.0
    
    def generate_health_report(self):
        """Generate comprehensive health report"""
        if not self.health_history:
            return "No health data available"
        
        latest_data = self.health_history[-1]['data']
        overall_health = latest_data.get('overall_health', {})
        sensors = latest_data.get('sensors', {})
        
        report = []
        report.append("=== SENSOR HEALTH REPORT ===")
        report.append(f"Timestamp: {time.ctime()}")
        report.append(f"Overall Health: {overall_health.get('health_percentage', 0):.1f}%")
        report.append(f"Active Sensors: {overall_health.get('active_sensors', 0)}/{overall_health.get('total_sensors', 0)}")
        
        # Health trend
        trend = self.get_health_trend()
        if trend > 0.1:
            trend_str = "IMPROVING"
        elif trend < -0.1:
            trend_str = "DEGRADING"
        else:
            trend_str = "STABLE"
        report.append(f"Health Trend: {trend_str} ({trend:.2f})")
        
        report.append("\n--- INDIVIDUAL SENSORS ---")
        for sensor_name, sensor_info in sensors.items():
            status = sensor_info.get('status', 'unknown')
            last_update = sensor_info.get('last_update')
            
            if last_update:
                time_since = time.time() - last_update
                time_str = f"{time_since:.1f}s ago"
            else:
                time_str = "Never"
            
            report.append(f"{sensor_name.upper()}: {status.upper()} (Last: {time_str})")
        
        return "\n".join(report)

def main(args=None):
    rclpy.init(args=args)
    
    try:
        monitor = SensorHealthMonitor()
        rclpy.spin(monitor)
    except KeyboardInterrupt:
        pass
    finally:
        if 'monitor' in locals():
            # Print final health report
            report = monitor.generate_health_report()
            monitor.get_logger().info(f"\n{report}")
            monitor.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
