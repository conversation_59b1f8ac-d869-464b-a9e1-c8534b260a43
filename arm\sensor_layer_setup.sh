#!/bin/bash

echo "=== Setting up Sensor Layer for Autonomous Car ==="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -d "src/sensors" ]; then
    print_error "Please run this script from the workspace root directory"
    exit 1
fi

print_status "Building sensor packages..."

# Build all packages
colcon build --packages-select camera_sensor gps_sensor imu_sensor gps_imu_fusion multi_sensor_fusion

if [ $? -eq 0 ]; then
    print_status "Build successful!"
else
    print_error "Build failed!"
    exit 1
fi

# Source the workspace
print_status "Sourcing workspace..."
source install/setup.bash

print_status "Sensor layer setup complete!"

echo ""
echo "=== Available Sensor Packages ==="
echo "1. camera_sensor     - Camera sensor with OpenCV processing"
echo "2. gps_sensor        - GPS sensor with simulator support"
echo "3. imu_sensor        - IMU sensor with simulator support"
echo "4. gps_imu_fusion    - GPS-IMU Extended Kalman Filter fusion"
echo "5. multi_sensor_fusion - Multi-sensor integration and health monitoring"

echo ""
echo "=== Usage Examples ==="
echo "# Launch individual sensors:"
echo "ros2 launch camera_sensor camera.launch.py"
echo "ros2 launch gps_sensor gps.launch.py"
echo "ros2 launch imu_sensor imu.launch.py"
echo ""
echo "# Launch sensor fusion:"
echo "ros2 launch gps_imu_fusion gps_imu_fusion.launch.py"
echo "ros2 launch multi_sensor_fusion multi_sensor_fusion.launch.py"
echo ""
echo "# Launch complete sensor layer:"
echo "ros2 launch sensor_layer.launch.py"
echo ""
echo "# Launch with specific sensors disabled:"
echo "ros2 launch sensor_layer.launch.py use_camera:=false use_imu:=false"

echo ""
echo "=== Monitoring Commands ==="
echo "# Check sensor topics:"
echo "ros2 topic list | grep autonomous_car"
echo ""
echo "# Monitor sensor health:"
echo "ros2 topic echo /autonomous_car/sensor_status"
echo ""
echo "# Monitor sensor alerts:"
echo "ros2 topic echo /autonomous_car/sensor_alerts"
echo ""
echo "# View sensor data:"
echo "ros2 topic echo /autonomous_car/camera/image_raw"
echo "ros2 topic echo /autonomous_car/gps/fix"
echo "ros2 topic echo /autonomous_car/imu/data"
echo "ros2 topic echo /autonomous_car/odometry/filtered"

print_status "Setup complete! You can now launch the sensor layer."
