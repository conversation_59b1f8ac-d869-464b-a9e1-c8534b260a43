[0.790s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.790s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=1, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x7ecaa15b7110>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7ecaa15565a0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7ecaa15565a0>>, mixin_verb=('build',))
[0.950s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.950s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.950s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.950s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.950s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.950s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.950s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/ros_ws'
[0.950s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.951s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.951s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.951s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.951s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.951s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.951s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.951s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.951s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[1.011s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[1.011s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[1.011s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[1.015s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[1.015s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[1.015s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[1.015s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[1.015s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[1.016s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[1.016s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[1.016s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[1.016s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[1.016s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[1.016s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[1.016s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[1.016s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[1.016s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[1.016s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[1.016s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[1.016s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[1.016s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[1.016s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[1.016s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[1.017s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[1.017s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[1.017s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[1.017s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[1.017s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[1.017s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[1.017s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[1.017s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[1.017s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[1.017s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[1.205s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[1.205s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[1.362s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 362 installed packages in /home/<USER>/ros2_jazzy/install
[1.389s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[8.874s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[8.875s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[8.875s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[8.875s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[8.875s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[8.875s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[8.897s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[8.897s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[8.897s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[9.042s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[9.154s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[9.154s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros_ws/install/local_setup.ps1'
[9.155s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ros_ws/install/_local_setup_util_ps1.py'
[9.156s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros_ws/install/setup.ps1'
[9.179s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros_ws/install/local_setup.sh'
[9.179s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ros_ws/install/_local_setup_util_sh.py'
[9.180s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros_ws/install/setup.sh'
[9.197s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros_ws/install/local_setup.bash'
[9.198s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros_ws/install/setup.bash'
[9.214s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros_ws/install/local_setup.zsh'
[9.215s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros_ws/install/setup.zsh'
