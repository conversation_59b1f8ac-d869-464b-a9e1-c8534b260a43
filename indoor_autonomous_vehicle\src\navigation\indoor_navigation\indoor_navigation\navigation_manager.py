#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import <PERSON>seS<PERSON><PERSON>, Twist, PoseWithCovarianceStamped
from nav_msgs.msg import OccupancyGrid, Path
from sensor_msgs.msg import LaserScan, Range
from std_msgs.msg import String, Bool
import json
import math
import numpy as np
from enum import Enum
import time

class NavigationState(Enum):
    IDLE = "idle"
    NAVIGATING = "navigating"
    OBSTACLE_AVOIDANCE = "obstacle_avoidance"
    REPLANNING = "replanning"
    PAUSED = "paused"
    EMERGENCY_STOP = "emergency_stop"
    MISSION_COMPLETE = "mission_complete"

class NavigationManager(Node):
    def __init__(self):
        super().__init__('navigation_manager')
        
        # Declare parameters
        self.declare_parameter('max_linear_speed', 0.5)
        self.declare_parameter('max_angular_speed', 1.0)
        self.declare_parameter('goal_tolerance', 0.2)
        self.declare_parameter('obstacle_threshold', 0.5)
        self.declare_parameter('emergency_threshold', 0.3)
        self.declare_parameter('replanning_timeout', 10.0)
        
        # Get parameters
        self.max_linear_speed = self.get_parameter('max_linear_speed').get_parameter_value().double_value
        self.max_angular_speed = self.get_parameter('max_angular_speed').get_parameter_value().double_value
        self.goal_tolerance = self.get_parameter('goal_tolerance').get_parameter_value().double_value
        self.obstacle_threshold = self.get_parameter('obstacle_threshold').get_parameter_value().double_value
        self.emergency_threshold = self.get_parameter('emergency_threshold').get_parameter_value().double_value
        self.replanning_timeout = self.get_parameter('replanning_timeout').get_parameter_value().double_value
        
        # Subscribers
        self.pose_sub = self.create_subscription(
            PoseWithCovarianceStamped, '/amcl_pose', self.pose_callback, 10)
        self.scan_sub = self.create_subscription(
            LaserScan, '/scan', self.scan_callback, 10)
        self.proximity_sub = self.create_subscription(
            Range, '/ultrasonic/proximity', self.proximity_callback, 10)
        self.goal_sub = self.create_subscription(
            PoseStamped, '/move_base_simple/goal', self.goal_callback, 10)
        self.mission_sub = self.create_subscription(
            String, '/navigation/mission', self.mission_callback, 10)
        
        # Publishers
        self.cmd_vel_pub = self.create_publisher(Twist, '/cmd_vel', 10)
        self.path_pub = self.create_publisher(Path, '/navigation/planned_path', 10)
        self.status_pub = self.create_publisher(String, '/navigation/status', 10)
        self.goal_reached_pub = self.create_publisher(Bool, '/navigation/goal_reached', 10)
        
        # Navigation state
        self.current_state = NavigationState.IDLE
        self.current_pose = None
        self.current_goal = None
        self.planned_path = []
        self.current_waypoint_index = 0
        
        # Sensor data
        self.latest_scan = None
        self.proximity_distance = float('inf')
        
        # Mission data
        self.current_mission = None
        self.mission_waypoints = []
        self.mission_start_time = None
        
        # Obstacle avoidance
        self.obstacle_detected = False
        self.last_replanning_time = 0
        
        # Recovery behavior
        self.recovery_attempts = 0
        self.max_recovery_attempts = 3
        
        # Timer for navigation loop
        self.navigation_timer = self.create_timer(0.1, self.navigation_loop)

        # Timer for status publishing
        self.status_timer = self.create_timer(1.0, self.publish_status)

        # Persistent storage for recovery
        self.last_known_position = None
        self.position_history = []
        self.max_history_length = 100
        
        self.get_logger().info('Navigation Manager started')
    
    def pose_callback(self, msg):
        """Update current robot pose"""
        self.current_pose = msg.pose.pose
    
    def scan_callback(self, msg):
        """Process LiDAR scan data"""
        self.latest_scan = msg
        self.check_obstacles_in_scan(msg)
    
    def proximity_callback(self, msg):
        """Process ultrasonic proximity data"""
        self.proximity_distance = msg.range
        
        # Emergency stop if too close
        if not math.isinf(msg.range) and msg.range < self.emergency_threshold:
            self.emergency_stop()
    
    def goal_callback(self, msg):
        """Receive new navigation goal"""
        self.set_navigation_goal(msg)
    
    def mission_callback(self, msg):
        """Receive mission with multiple waypoints"""
        try:
            mission_data = json.loads(msg.data)
            self.start_mission(mission_data)
        except json.JSONDecodeError as e:
            self.get_logger().error(f'Invalid mission format: {e}')
    
    def set_navigation_goal(self, goal_pose):
        """Set a single navigation goal"""
        self.current_goal = goal_pose
        self.current_state = NavigationState.NAVIGATING
        self.recovery_attempts = 0
        
        # Plan path to goal
        self.plan_path_to_goal()
        
        self.get_logger().info(f'New goal set: ({goal_pose.pose.position.x:.2f}, {goal_pose.pose.position.y:.2f})')
    
    def start_mission(self, mission_data):
        """Start a mission with multiple waypoints"""
        self.current_mission = mission_data
        self.mission_waypoints = mission_data.get('waypoints', [])
        self.current_waypoint_index = 0
        self.mission_start_time = time.time()
        
        if self.mission_waypoints:
            # Set first waypoint as goal
            first_waypoint = self.mission_waypoints[0]
            goal = PoseStamped()
            goal.header.frame_id = 'map'
            goal.header.stamp = self.get_clock().now().to_msg()
            goal.pose.position.x = first_waypoint['x']
            goal.pose.position.y = first_waypoint['y']
            goal.pose.orientation.w = 1.0
            
            self.set_navigation_goal(goal)
            
            self.get_logger().info(f'Mission started with {len(self.mission_waypoints)} waypoints')
    
    def plan_path_to_goal(self):
        """Plan path from current position to goal using A* algorithm"""
        if not self.current_pose or not self.current_goal:
            return
        
        # Simple straight-line path for now
        # In practice, use A* or other path planning algorithm
        start_x = self.current_pose.position.x
        start_y = self.current_pose.position.y
        goal_x = self.current_goal.pose.position.x
        goal_y = self.current_goal.pose.position.y
        
        # Create path with intermediate points
        num_points = 10
        path_points = []
        
        for i in range(num_points + 1):
            t = i / num_points
            x = start_x + t * (goal_x - start_x)
            y = start_y + t * (goal_y - start_y)
            path_points.append((x, y))
        
        self.planned_path = path_points
        self.publish_planned_path()
    
    def publish_planned_path(self):
        """Publish the planned path for visualization"""
        if not self.planned_path:
            return
        
        path_msg = Path()
        path_msg.header.frame_id = 'map'
        path_msg.header.stamp = self.get_clock().now().to_msg()
        
        for x, y in self.planned_path:
            pose = PoseStamped()
            pose.header.frame_id = 'map'
            pose.pose.position.x = x
            pose.pose.position.y = y
            pose.pose.orientation.w = 1.0
            path_msg.poses.append(pose)
        
        self.path_pub.publish(path_msg)
    
    def check_obstacles_in_scan(self, scan):
        """Check for obstacles in LiDAR scan"""
        if not scan.ranges:
            return
        
        # Check front sector for obstacles
        front_sector_size = 60  # degrees
        front_indices = []
        
        total_points = len(scan.ranges)
        angle_per_point = (scan.angle_max - scan.angle_min) / total_points
        
        for i, range_val in enumerate(scan.ranges):
            angle = scan.angle_min + i * angle_per_point
            angle_deg = math.degrees(angle)
            
            # Check front sector (-30 to +30 degrees)
            if -front_sector_size/2 <= angle_deg <= front_sector_size/2:
                if not math.isinf(range_val) and range_val < self.obstacle_threshold:
                    self.obstacle_detected = True
                    return
        
        self.obstacle_detected = False
    
    def navigation_loop(self):
        """Main navigation control loop"""
        if self.current_state == NavigationState.IDLE:
            self.stop_robot()
            
        elif self.current_state == NavigationState.NAVIGATING:
            self.navigate_to_goal()
            
        elif self.current_state == NavigationState.OBSTACLE_AVOIDANCE:
            self.avoid_obstacles()
            
        elif self.current_state == NavigationState.REPLANNING:
            self.replan_path()
            
        elif self.current_state == NavigationState.EMERGENCY_STOP:
            self.stop_robot()
            self.check_emergency_recovery()
            
        elif self.current_state == NavigationState.PAUSED:
            self.stop_robot()
    
    def navigate_to_goal(self):
        """Navigate towards the current goal"""
        if not self.current_pose or not self.current_goal:
            return
        
        # Check if goal is reached
        if self.is_goal_reached():
            self.goal_reached()
            return
        
        # Check for obstacles
        if self.obstacle_detected or self.proximity_distance < self.obstacle_threshold:
            self.current_state = NavigationState.OBSTACLE_AVOIDANCE
            return
        
        # Calculate control commands
        cmd = self.calculate_control_command()
        self.cmd_vel_pub.publish(cmd)
    
    def calculate_control_command(self):
        """Calculate velocity commands to reach goal"""
        cmd = Twist()
        
        if not self.current_pose or not self.current_goal:
            return cmd
        
        # Calculate distance and angle to goal
        dx = self.current_goal.pose.position.x - self.current_pose.position.x
        dy = self.current_goal.pose.position.y - self.current_pose.position.y
        distance = math.sqrt(dx*dx + dy*dy)
        
        # Calculate desired heading
        desired_yaw = math.atan2(dy, dx)
        
        # Get current yaw from quaternion
        current_yaw = self.get_yaw_from_quaternion(self.current_pose.orientation)
        
        # Calculate angular error
        angular_error = self.normalize_angle(desired_yaw - current_yaw)
        
        # Control gains
        linear_gain = 0.5
        angular_gain = 1.0
        
        # Calculate velocities
        if abs(angular_error) > 0.2:  # Turn first if large angular error
            cmd.linear.x = 0.0
            cmd.angular.z = max(-self.max_angular_speed, 
                              min(self.max_angular_speed, angular_gain * angular_error))
        else:
            cmd.linear.x = max(0.0, min(self.max_linear_speed, linear_gain * distance))
            cmd.angular.z = max(-self.max_angular_speed, 
                              min(self.max_angular_speed, angular_gain * angular_error))
        
        return cmd
    
    def avoid_obstacles(self):
        """Simple obstacle avoidance behavior"""
        cmd = Twist()
        
        # Stop and turn away from obstacle
        if self.proximity_distance < self.emergency_threshold:
            self.emergency_stop()
            return
        
        # Simple avoidance: turn left
        cmd.linear.x = 0.1
        cmd.angular.z = 0.5
        
        self.cmd_vel_pub.publish(cmd)
        
        # Check if obstacle is cleared
        if not self.obstacle_detected and self.proximity_distance > self.obstacle_threshold * 1.5:
            self.current_state = NavigationState.REPLANNING
            self.last_replanning_time = time.time()
    
    def replan_path(self):
        """Replan path after obstacle avoidance"""
        current_time = time.time()
        
        if current_time - self.last_replanning_time > 2.0:  # Wait 2 seconds
            self.plan_path_to_goal()
            self.current_state = NavigationState.NAVIGATING
            self.get_logger().info('Path replanned after obstacle avoidance')
    
    def emergency_stop(self):
        """Emergency stop the robot"""
        self.current_state = NavigationState.EMERGENCY_STOP
        self.stop_robot()
        self.get_logger().warn('Emergency stop activated!')
    
    def check_emergency_recovery(self):
        """Check if emergency condition is cleared"""
        if self.proximity_distance > self.emergency_threshold * 1.2:
            self.current_state = NavigationState.NAVIGATING
            self.get_logger().info('Emergency condition cleared, resuming navigation')
    
    def is_goal_reached(self):
        """Check if current goal is reached"""
        if not self.current_pose or not self.current_goal:
            return False
        
        dx = self.current_goal.pose.position.x - self.current_pose.position.x
        dy = self.current_goal.pose.position.y - self.current_pose.position.y
        distance = math.sqrt(dx*dx + dy*dy)
        
        return distance < self.goal_tolerance
    
    def goal_reached(self):
        """Handle goal reached event"""
        self.stop_robot()
        
        # Publish goal reached
        goal_reached_msg = Bool()
        goal_reached_msg.data = True
        self.goal_reached_pub.publish(goal_reached_msg)
        
        # Check if this is part of a mission
        if self.current_mission and self.mission_waypoints:
            self.current_waypoint_index += 1
            
            if self.current_waypoint_index < len(self.mission_waypoints):
                # Move to next waypoint
                next_waypoint = self.mission_waypoints[self.current_waypoint_index]
                goal = PoseStamped()
                goal.header.frame_id = 'map'
                goal.header.stamp = self.get_clock().now().to_msg()
                goal.pose.position.x = next_waypoint['x']
                goal.pose.position.y = next_waypoint['y']
                goal.pose.orientation.w = 1.0
                
                self.set_navigation_goal(goal)
                self.get_logger().info(f'Moving to waypoint {self.current_waypoint_index + 1}/{len(self.mission_waypoints)}')
            else:
                # Mission complete
                self.current_state = NavigationState.MISSION_COMPLETE
                self.get_logger().info('Mission completed!')
        else:
            # Single goal navigation complete
            self.current_state = NavigationState.IDLE
            self.get_logger().info('Goal reached!')
    
    def stop_robot(self):
        """Stop the robot"""
        cmd = Twist()
        self.cmd_vel_pub.publish(cmd)
    
    def get_yaw_from_quaternion(self, quaternion):
        """Convert quaternion to yaw angle"""
        # Simplified conversion
        siny_cosp = 2 * (quaternion.w * quaternion.z + quaternion.x * quaternion.y)
        cosy_cosp = 1 - 2 * (quaternion.y * quaternion.y + quaternion.z * quaternion.z)
        return math.atan2(siny_cosp, cosy_cosp)
    
    def normalize_angle(self, angle):
        """Normalize angle to [-pi, pi]"""
        while angle > math.pi:
            angle -= 2 * math.pi
        while angle < -math.pi:
            angle += 2 * math.pi
        return angle
    
    def publish_status(self):
        """Publish navigation status"""
        status_data = {
            'state': self.current_state.value,
            'current_pose': {
                'x': self.current_pose.position.x if self.current_pose else 0.0,
                'y': self.current_pose.position.y if self.current_pose else 0.0
            } if self.current_pose else None,
            'current_goal': {
                'x': self.current_goal.pose.position.x if self.current_goal else 0.0,
                'y': self.current_goal.pose.position.y if self.current_goal else 0.0
            } if self.current_goal else None,
            'obstacle_detected': self.obstacle_detected,
            'proximity_distance': self.proximity_distance if not math.isinf(self.proximity_distance) else None,
            'mission_progress': {
                'current_waypoint': self.current_waypoint_index + 1,
                'total_waypoints': len(self.mission_waypoints)
            } if self.mission_waypoints else None
        }
        
        status_msg = String()
        status_msg.data = json.dumps(status_data, indent=2)
        self.status_pub.publish(status_msg)

def main(args=None):
    rclpy.init(args=args)
    
    try:
        navigation_manager = NavigationManager()
        rclpy.spin(navigation_manager)
    except KeyboardInterrupt:
        pass
    finally:
        if 'navigation_manager' in locals():
            navigation_manager.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
